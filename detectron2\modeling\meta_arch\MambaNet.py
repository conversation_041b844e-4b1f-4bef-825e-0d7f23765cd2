# models.py
import torch
import torch.nn as nn
import torch.nn.functional as F
from .layers import QuanConv
from .mamba import Mamba, ModelArgs

class Net(torch.nn.Module):
    def __init__(self):
        super(Net, self).__init__()
        # 量子卷积层，设置了in_channels=256，out_channels=1，kernel_size=2，n_qubits=4
        self.quanconv = QuanConv(in_channels=256, out_channels=1, kernel_size=2, n_qubits=4, shots=1)
        # Dropout层
        self.dropout = torch.nn.Dropout2d()
        # 用于动态调整通道数的卷积层
        self.conv_adjust_channels = nn.ModuleDict()
        self.conv_1x1_adjust_channels = nn.ModuleDict()
        self.fc1 = nn.ModuleDict()  # 动态创建 fc1 层
        self.fc2 = nn.Linear(8, 16)  # 减少全连接层的参数数量

        # 初始化 Mamba 部分
        mamba_args = ModelArgs(
            d_model=16,
            n_layer=1,  # 减少层数
            vocab_size=1000  # 根据实际需求调整
        )
        self.mamba = Mamba(mamba_args)

    def forward(self, features):
        device = features[list(features.keys())[0]].device  # 获取输入张量的设备
        enhanced_features = {}

        # 遍历输入特征字典，分别处理每个特征图
        for key, x in features.items():
            # 确保输入张量位于正确的设备
            x = x.to(device)

            # 保存原始输入尺寸
            original_shape = x.shape  # 获取批次、通道、高度和宽度

            # 通过量子卷积层
            x = F.relu(self.quanconv(x))
            # print(f"quanconv output shape for {key}: {x.shape}")

            # 进行最大池化
            x = F.max_pool2d(x, 2)
            # print(f"After max_pool2d: {x.shape}")

            _, _, H, W = x.size()
            kernel_size = min(max(3, min(H, W)), 5)  # 使用3x3到5x5之间的卷积核

            # 在第一次使用时创建卷积层
            if key not in self.conv_adjust_channels:
                self.conv_adjust_channels[key] = torch.nn.Conv2d(1, 4, kernel_size=kernel_size, padding=1).to(device)

            # 通过常规卷积层
            x = F.relu(self.conv_adjust_channels[key](x))
            # print(f"After conv: {x.shape}")

            # 进行最大池化
            x = F.max_pool2d(x, 2)
            # print(f"After max_pool2d again: {x.shape}")

            # 进行Dropout
            x = self.dropout(x)
            # print(f"After dropout: {x.shape}")

            # 展平张量
            x = torch.flatten(x, start_dim=1)
            # print(f"After flatten: {x.shape}")

            # 动态计算展平后的维度
            flattened_size = x.size(1)
            # print(f"Flattened size: {flattened_size}")

            # 动态创建fc1层，根据展平后的尺寸计算输入大小
            if key not in self.fc1:
                self.fc1[key] = nn.Linear(flattened_size, 8).to(device)
            x = F.relu(self.fc1[key](x))
            # print(f"After fc1: {x.shape}")

            # 通过第二个全连接层
            x = self.fc2(x)
            # print(f"After fc2: {x.shape}")

            # 将展平后的特征传递给 Mamba
            x = x.unsqueeze(1)  # 添加序列长度维度 (b, 1, 16)
            x = self.mamba(x)
            # print(f"After Mamba: {x.shape}")

            # 移除序列长度维度 (b, 16)
            x = x.squeeze(1)

            # 重新调整形状以匹配原始特征图的通道数
            x = x.view(original_shape[0], 16, 1, 1).expand(original_shape[0], 16, original_shape[2], original_shape[3])

            # 将输出与原始特征图相加
            x = x + features[key]
            # print(f"After adding original features: {x.shape}")

            # 将输出存入字典中
            enhanced_features[key] = x
            # print(f"Enhanced features for {key}: {x}")

        return enhanced_features
