# require an issue template to be chosen
blank_issues_enabled: false

contact_links:
  - name: How-To / All Other Questions
    url: https://github.com/facebookresearch/detectron2/discussions
    about: Use "github discussions" for community support on general questions that don't belong to the above issue categories
  - name: Detectron2 Documentation
    url: https://detectron2.readthedocs.io/index.html
    about: Check if your question is answered in tutorials or API docs

# Unexpected behaviors & bugs are split to two templates.
# When they are one template, users think "it's not a bug" and don't choose the template.
#
# But the file name is still "unexpected-problems-bugs.md" so that old references
# to this issue template still works.
# It's ok since this template should be a superset of "bugs.md" (unexpected behaviors is a superset of bugs)
