_BASE_: "Base-DensePose-RCNN-FPN.yaml"
MODEL:
  WEIGHTS: "detectron2://ImageNetPretrained/MSRA/R-101.pkl"
  RESNETS:
    DEPTH: 101
  ROI_DENSEPOSE_HEAD:
    NAME: "DensePoseDeepLabHead"
    UV_CONFIDENCE:
      ENABLED: True
      TYPE: "indep_aniso"
    SEGM_CONFIDENCE:
      ENABLED: True
    POINT_REGRESSION_WEIGHTS: 0.0005
SOLVER:
  CLIP_GRADIENTS:
    ENABLED: True
  MAX_ITER: 130000
  STEPS: (100000, 120000)
