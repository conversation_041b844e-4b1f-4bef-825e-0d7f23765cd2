This is XeTeX, Version 3.141592653-2.6-0.999995 (MiKTeX 24.1) (preloaded format=xelatex 2025.8.1)  2 AUG 2025 08:23
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**d:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex
(d:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-01-04>
(C:\Program Files\MiKTeX\tex/latex/ieeetran\IEEEtran.cls
Document Class: IEEEtran 2015/08/26 V1.8b by <PERSON>
-- See the "IEEEtran_HOWTO" manual for usage information.
-- http://www.michaelshell.org/tex/ieeetran/
\@IEEEtrantmpdimenA=\dimen140
\@IEEEtrantmpdimenB=\dimen141
\@IEEEtrantmpdimenC=\dimen142
\@IEEEtrantmpcountA=\count183
\@IEEEtrantmpcountB=\count184
\@IEEEtrantmpcountC=\count185
\@IEEEtrantmptoksA=\toks17
LaTeX Font Info:    Trying to load font information for TU+ptm on input line 503.
LaTeX Font Info:    No file TUptm.fd. on input line 503.

LaTeX Font Warning: Font shape `TU/ptm/m/n' undefined
(Font)              using `TU/lmr/m/n' instead on input line 503.

-- Using 8.5in x 11in (letter) paper.
-- Using DVI output.
\@IEEEnormalsizeunitybaselineskip=\dimen143
-- This is a 10 point document.
\CLASSINFOnormalsizebaselineskip=\dimen144
\CLASSINFOnormalsizeunitybaselineskip=\dimen145
\IEEEnormaljot=\dimen146

LaTeX Font Warning: Font shape `TU/ptm/bx/n' undefined
(Font)              using `TU/ptm/m/n' instead on input line 1090.


LaTeX Font Warning: Font shape `TU/ptm/m/it' undefined
(Font)              using `TU/ptm/m/n' instead on input line 1090.


LaTeX Font Warning: Font shape `TU/ptm/bx/it' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 1090.

\IEEEquantizedlength=\dimen147
\IEEEquantizedlengthdiff=\dimen148
\IEEEquantizedtextheightdiff=\dimen149
\IEEEilabelindentA=\dimen150
\IEEEilabelindentB=\dimen151
\IEEEilabelindent=\dimen152
\IEEEelabelindent=\dimen153
\IEEEdlabelindent=\dimen154
\IEEElabelindent=\dimen155
\IEEEiednormlabelsep=\dimen156
\IEEEiedmathlabelsep=\dimen157
\IEEEiedtopsep=\skip48
\c@section=\count186
\c@subsection=\count187
\c@subsubsection=\count188
\c@paragraph=\count189
\c@IEEEsubequation=\count190
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\c@figure=\count191
\c@table=\count192
\@IEEEeqnnumcols=\count193
\@IEEEeqncolcnt=\count194
\@IEEEsubeqnnumrollback=\count195
\@IEEEquantizeheightA=\dimen158
\@IEEEquantizeheightB=\dimen159
\@IEEEquantizeheightC=\dimen160
\@IEEEquantizeprevdepth=\dimen161
\@IEEEquantizemultiple=\count196
\@IEEEquantizeboxA=\box51
\@IEEEtmpitemindent=\dimen162
\IEEEPARstartletwidth=\dimen163
\c@IEEEbiography=\count197
\@IEEEtranrubishbin=\box52
) (C:\Program Files\MiKTeX\tex/latex/cite\cite.sty
LaTeX Info: Redefining \cite on input line 302.
LaTeX Info: Redefining \nocite on input line 332.
Package: cite 2015/02/27  v 5.5
) (C:\Program Files\MiKTeX\tex/latex/graphics\graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (C:\Program Files\MiKTeX\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks18
) (C:\Program Files\MiKTeX\tex/latex/graphics\graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)
 (C:\Program Files\MiKTeX\tex/latex/graphics\trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
) (C:\Program Files\MiKTeX\tex/latex/graphics-cfg\graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 107.
 (C:\Program Files\MiKTeX\tex/latex/graphics-def\pdftex.def
File: pdftex.def 2022/09/22 v1.2b Graphics/color driver for pdftex
))
\Gin@req@height=\dimen164
\Gin@req@width=\dimen165
) (C:\Program Files\MiKTeX\tex/latex/amsmath\amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(C:\Program Files\MiKTeX\tex/latex/amsmath\amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (C:\Program Files\MiKTeX\tex/latex/amsmath\amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks19
\ex@=\dimen166
)) (C:\Program Files\MiKTeX\tex/latex/amsmath\amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen167
) (C:\Program Files\MiKTeX\tex/latex/amsmath\amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count198
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count199
\leftroot@=\count266
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count267
\DOTSCASE@=\count268
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box53
\strutbox@=\box54
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen168
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count269
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count270
\dotsspace@=\muskip16
\c@parentequation=\count271
\dspbrk@lvl=\count272
\tag@help=\toks20
\row@=\count273
\column@=\count274
\maxfields@=\count275
\andhelp@=\toks21
\eqnshift@=\dimen169
\alignsep@=\dimen170
\tagshift@=\dimen171
\tagwidth@=\dimen172
\totwidth@=\dimen173
\lineht@=\dimen174
\@envbody=\toks22
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks23
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (C:\Program Files\MiKTeX\tex/latex/amsfonts\amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
) (C:\Program Files\MiKTeX\tex/latex/amsfonts\amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
) (C:\Program Files\MiKTeX\tex/latex/algorithms\algorithmic.sty
Invalid UTF-8 byte or sequence at line 11 replaced by U+FFFD.
Package: algorithmic 2009/08/24 v0.1 Document Style `algorithmic'
 (C:\Program Files\MiKTeX\tex/latex/base\ifthen.sty
Package: ifthen 2022/04/13 v1.1d Standard LaTeX ifthen package (DPC)
)
\c@ALC@unique=\count276
\c@ALC@line=\count277
\c@ALC@rem=\count278
\c@ALC@depth=\count279
\ALC@tlm=\skip54
\algorithmicindent=\skip55
) (C:\Program Files\MiKTeX\tex/latex/tools\array.sty
Package: array 2023/10/16 v2.5g Tabular extension package (FMi)
\col@sep=\dimen175
\ar@mcellbox=\box55
\extrarowheight=\dimen176
\NC@list=\toks24
\extratabsurround=\skip56
\backup@length=\skip57
\ar@cellbox=\box56
) (C:\Program Files\MiKTeX\tex/latex/mdwtools\mdwmath.sty
Package: mdwmath 1996/04/11 1.1 Nice mathematical things
\sq@sqrt=\count280
LaTeX Info: Redefining \sqrt on input line 84.
) (C:\Program Files\MiKTeX\tex/latex/mdwtools\mdwtab.sty
Package: mdwtab 1998/04/28 1.9 Table typesetting with style
\tab@state=\count281
\tab@columns=\count282
\tab@preamble=\toks25
\tab@shortline=\toks26
\extrarowheight=\dimen177
\tabextrasep=\dimen178
\arrayextrasep=\dimen179
\smarraycolsep=\dimen180
\smarrayextrasep=\dimen181
\tab@width=\dimen182
\col@sep=\dimen183
\tab@endheight=\dimen184
\tab@leftskip=\skip58
\tab@rightskip=\skip59
\fn@notes=\box57
\fn@width=\dimen185
) (C:\Program Files\MiKTeX\tex/latex/eqparbox\eqparbox.sty
Package: eqparbox 2017/09/03 v4.1 Create equal-widthed boxes
\eqp@tempdima=\skip60
\eqp@tempdimb=\skip61
\eqp@tabular@box=\box58
\eqp@list@box=\box59
\eqp@list@indent=\skip62
 (C:\Program Files\MiKTeX\tex/latex/environ\environ.sty
Package: environ 2014/05/04 v0.3 A new way to define environments
 (C:\Program Files\MiKTeX\tex/latex/trimspaces\trimspaces.sty
Package: trimspaces 2009/09/17 v1.1 Trim spaces around a token list
))) (C:\Program Files\MiKTeX\tex/latex/url\url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
) (C:\Program Files\MiKTeX\tex/latex/subfigure\subfigure.sty
Package: subfigure 2002/07/30 v2.1.4 subfigure package
\subfigtopskip=\skip63
\subfigcapskip=\skip64
\subfigcaptopadj=\dimen186
\subfigbottomskip=\skip65
\subfigcapmargin=\dimen187
\subfiglabelskip=\skip66
\c@subfigure=\count283
\c@lofdepth=\count284
\c@subtable=\count285
\c@lotdepth=\count286

****************************************
* Local config file subfigure.cfg used *
****************************************
(C:\Program Files\MiKTeX\tex/latex/subfigure\subfigure.cfg)
\subfig@top=\skip67
\subfig@bottom=\skip68
) (C:\Program Files\MiKTeX\tex/latex/multirow\multirow.sty
Package: multirow 2024/11/12 v2.9 Span multiple rows of a table
\multirow@colwidth=\skip69
\multirow@cntb=\count287
\multirow@dima=\skip70
\bigstrutjot=\dimen188
) (C:\Program Files\MiKTeX\tex/latex/booktabs\booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen189
\lightrulewidth=\dimen190
\cmidrulewidth=\dimen191
\belowrulesep=\dimen192
\belowbottomsep=\dimen193
\aboverulesep=\dimen194
\abovetopsep=\dimen195
\cmidrulesep=\dimen196
\cmidrulekern=\dimen197
\defaultaddspace=\dimen198
\@cmidla=\count288
\@cmidlb=\count289
\@aboverulesep=\dimen199
\@belowrulesep=\dimen256
\@thisruleclass=\count290
\@lastruleclass=\count291
\@thisrulewidth=\dimen257
) (C:\Program Files\MiKTeX\tex/latex/graphics\color.sty
Package: color 2022/01/06 v1.3d Standard LaTeX Color (DPC)
 (C:\Program Files\MiKTeX\tex/latex/graphics-cfg\color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package color Info: Driver file: xetex.def on input line 149.
 (C:\Program Files\MiKTeX\tex/latex/graphics-def\xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
) (C:\Program Files\MiKTeX\tex/latex/graphics\mathcolor.ltx)) (C:\Program Files\MiKTeX\tex/latex/preprint\balance.sty
Package: balance 1999/02/23 4.3 (PWD)
\oldvsize=\dimen258
) (C:\Program Files\MiKTeX\tex/latex/hyperref\hyperref.sty
Package: hyperref 2023-11-26 v7.01g Hypertext links for LaTeX
 (C:\Program Files\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
) (C:\Program Files\MiKTeX\tex/generic/infwarerr\infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
) (C:\Program Files\MiKTeX\tex/latex/kvsetkeys\kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
) (C:\Program Files\MiKTeX\tex/generic/kvdefinekeys\kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (C:\Program Files\MiKTeX\tex/generic/pdfescape\pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
 (C:\Program Files\MiKTeX\tex/generic/ltxcmds\ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (C:\Program Files\MiKTeX\tex/generic/pdftexcmds\pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode not found.
)) (C:\Program Files\MiKTeX\tex/latex/hycolor\hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (C:\Program Files\MiKTeX\tex/latex/letltxmacro\letltxmacro.sty
Package: letltxmacro 2019/12/03 v1.6 Let assignment for LaTeX macros (HO)
) (C:\Program Files\MiKTeX\tex/latex/auxhook\auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
) (C:\Program Files\MiKTeX\tex/latex/hyperref\nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section
 (C:\Program Files\MiKTeX\tex/latex/refcount\refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (C:\Program Files\MiKTeX\tex/generic/gettitlestring\gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
 (C:\Program Files\MiKTeX\tex/latex/kvoptions\kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count292
) (C:\Program Files\MiKTeX\tex/latex/etoolbox\etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count293
)
\@linkdim=\dimen259
\Hy@linkcounter=\count294
\Hy@pagecounter=\count295
 (C:\Program Files\MiKTeX\tex/latex/hyperref\pd1enc.def
File: pd1enc.def 2023-11-26 v7.01g Hyperref: PDFDocEncoding definition (HO)
) (C:\Program Files\MiKTeX\tex/generic/intcalc\intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count296
 (C:\Program Files\MiKTeX\tex/latex/hyperref\puenc.def
File: puenc.def 2023-11-26 v7.01g Hyperref: PDF Unicode definition (HO)
)

C:\Program Files\MiKTeX\tex/latex/hyperref\hyperref.sty:4064: Package hyperref Error: Wrong driver option `pdftex',
(hyperref)                because pdfTeX in PDF mode is not detected.

See the hyperref package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.4064 \ProcessKeyvalOptions{Hyp}
                                 
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

Package hyperref Info: Option `colorlinks' set `true' on input line 4064.
Package hyperref Info: Option `bookmarks' set `false' on input line 4064.
Package hyperref Info: Hyper figures OFF on input line 4181.
Package hyperref Info: Link nesting OFF on input line 4186.
Package hyperref Info: Hyper index ON on input line 4189.
Package hyperref Info: Plain pages OFF on input line 4196.
Package hyperref Info: Backreferencing OFF on input line 4201.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks OFF on input line 4454.
\c@Hy@tempcnt=\count297
LaTeX Info: Redefining \url on input line 4786.
\XeTeXLinkMargin=\dimen260
(C:\Program Files\MiKTeX\tex/generic/bitset\bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (C:\Program Files\MiKTeX\tex/generic/bigintcalc\bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count298
\Field@Width=\dimen261
\Fld@charsize=\dimen262
Package hyperref Info: Hyper figures OFF on input line 6065.
Package hyperref Info: Link nesting OFF on input line 6070.
Package hyperref Info: Hyper index ON on input line 6073.
Package hyperref Info: backreferencing OFF on input line 6080.
Package hyperref Info: Link coloring ON on input line 6083.
Package hyperref Info: Link coloring with OCG OFF on input line 6090.
Package hyperref Info: PDF/A mode OFF on input line 6095.
 (C:\Program Files\MiKTeX\tex/latex/base\atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count299
\c@Item=\count300
\c@Hfootnote=\count301
)
Package hyperref Info: Driver (autodetected): hxetex.
 (C:\Program Files\MiKTeX\tex/latex/hyperref\hxetex.def
File: hxetex.def 2023-11-26 v7.01g Hyperref driver for XeTeX
 (C:\Program Files\MiKTeX\tex/generic/stringenc\stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO)
)
\pdfm@box=\box60
\c@Hy@AnnotLevel=\count302
\HyField@AnnotCount=\count303
\Fld@listcount=\count304
\c@bookmark@seq@number=\count305
 (C:\Program Files\MiKTeX\tex/latex/rerunfilecheck\rerunfilecheck.sty
Package: rerunfilecheck 2025-06-21 v1.11 Rerun checks for auxiliary files (HO)
 (C:\Program Files\MiKTeX\tex/generic/uniquecounter\uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 284.
)
\Hy@SectionHShift=\skip71
) (C:\Program Files\MiKTeX\tex/latex/l3backend\l3backend-xetex.def
File: l3backend-xetex.def 2024-01-04 L3 backend support: XeTeX
\g__graphics_track_int=\count306
\l__pdf_internal_box=\box61
\g__pdf_backend_object_int=\count307
\g__pdf_backend_annotation_int=\count308
\g__pdf_backend_link_int=\count309
)
No file ieee_tgrs_paper.aux.
\openout1 = `ieee_tgrs_paper.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 27.
LaTeX Font Info:    ... okay on input line 27.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 27.
LaTeX Font Info:    ... okay on input line 27.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 27.
LaTeX Font Info:    ... okay on input line 27.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 27.
LaTeX Font Info:    ... okay on input line 27.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 27.
LaTeX Font Info:    Trying to load font information for TS1+cmr on input line 27.
(C:\Program Files\MiKTeX\tex/latex/base\ts1cmr.fd
File: ts1cmr.fd 2023/04/13 v2.5m Standard LaTeX font definitions
)
LaTeX Font Info:    ... okay on input line 27.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 27.
LaTeX Font Info:    ... okay on input line 27.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 27.
LaTeX Font Info:    ... okay on input line 27.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 27.
LaTeX Font Info:    ... okay on input line 27.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 27.
LaTeX Font Info:    ... okay on input line 27.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 27.
LaTeX Font Info:    ... okay on input line 27.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 27.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 27.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 27.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 27.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 27.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 27.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 27.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 27.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 27.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 27.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 27.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 27.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 27.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 27.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 27.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 27.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 27.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 27.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 27.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 27.

-- Lines per column: 58 (exact).
(C:\Program Files\MiKTeX\tex/context/base/mkii\supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count310
\scratchdimen=\dimen263
\scratchbox=\box62
\nofMPsegments=\count311
\nofMParguments=\count312
\everyMPshowfont=\toks27
\MPscratchCnt=\count313
\MPscratchDim=\dimen264
\MPnumerator=\count314
\makeMPintoPDFobject=\count315
\everyMPtoPDFconversion=\toks28
)
d:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:27: Undefined control sequence.
<argument> \ifnum \pdfshellescape 
                                  >0 \edef \Gin@extensions {\Gin@extensions ...
l.27 \begin{document}
                     
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

d:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:27: Missing number, treated as zero.
<to be read again> 
                   >
l.27 \begin{document}
                     
A number should have been here; I inserted `0'.
(If you can't figure out why I needed to see a number,
look up `weird error' in the index to The TeXbook.)

Package hyperref Info: Link coloring ON on input line 27.

Package hyperref Warning: Rerun to get /PageLabels entry.


Underfull \hbox (badness 1983) in paragraph at lines 43--43
\TU/ptm/bx/it/8 versity of Agriculture, City, State 12345 USA (e-mail: first-
 []


Underfull \hbox (badness 4254) in paragraph at lines 43--43
[][][]\TU/ptm/bx/it/8 T. Lastname is with the Department of Computer Sci-
 []


Underfull \hbox (badness 2103) in paragraph at lines 43--43
\TU/ptm/bx/it/8 ence, Technology University, City, State 12345 USA (e-mail:
 []


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 44.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 48.


LaTeX Font Warning: Font shape `TU/ptm/m/sc' undefined
(Font)              using `TU/ptm/m/n' instead on input line 54.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 55.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 55.


LaTeX Warning: Citation `furbank2011phenomics' on page 1 undefined on input line 55.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 55.


LaTeX Warning: Citation `yang2020crop' on page 1 undefined on input line 55.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 57.


LaTeX Warning: Citation `zhang2019precision' on page 1 undefined on input line 57.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 59.


LaTeX Warning: Citation `ma2019deep' on page 1 undefined on input line 59.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 64.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 65.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 66.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 67.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 68.


Underfull \vbox (badness 2165) has occurred while \output is active []

 [1


]
LaTeX Font Info:    Trying to load font information for U+msa on input line 74.
 (C:\Program Files\MiKTeX\tex/latex/amsfonts\umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 74.
 (C:\Program Files\MiKTeX\tex/latex/amsfonts\umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
Underfull \hbox (badness 4303) in paragraph at lines 75--76
[]\TU/ptm/bx/it/10 An attention-based mechanism that adaptively
 []


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 86.


LaTeX Warning: Citation `rouse1974monitoring' on page 2 undefined on input line 86.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 86.


LaTeX Warning: Citation `huete2002overview' on page 2 undefined on input line 86.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 88.


LaTeX Warning: Citation `blackburn2007spectral' on page 2 undefined on input line 88.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 88.


LaTeX Warning: Citation `verrelst2015optical' on page 2 undefined on input line 88.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 90.


LaTeX Warning: Citation `hosoi2006voxel' on page 2 undefined on input line 90.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 90.


LaTeX Warning: Citation `jimenez2018lidar' on page 2 undefined on input line 90.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 94.


LaTeX Warning: Citation `zhu2017deep' on page 2 undefined on input line 94.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 94.


LaTeX Warning: Citation `zhang2016deep' on page 2 undefined on input line 94.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 96.


LaTeX Warning: Citation `kamilaris2018deep' on page 2 undefined on input line 96.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 96.


LaTeX Warning: Citation `ndikumana2018deep' on page 2 undefined on input line 96.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 100.


LaTeX Warning: Citation `lahat2015multimodal' on page 2 undefined on input line 100.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 100.


LaTeX Warning: Citation `audebert2019beyond' on page 2 undefined on input line 100.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 102.


LaTeX Warning: Citation `xu2015show' on page 2 undefined on input line 102.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 102.


LaTeX Warning: Citation `vaswani2017attention' on page 2 undefined on input line 102.

[2]

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 153.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 154.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 155.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 176.


LaTeX Warning: Reference `tab:results' on page 3 undefined on input line 176.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 185.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 185.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 185.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 185.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 185.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 186.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 186.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 186.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 186.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 192.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 192.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 192.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 192.


Overfull \hbox (0.98604pt too wide) in paragraph at lines 183--195
 [][] 
 []


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 204.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 205.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 206.

(ieee_tgrs_paper.bbl [3])

LaTeX Warning: File `author1.jpg' not found on input line 223.

d:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:223: Unable to load picture or PDF file 'author1.jpg'.
<to be read again> 
                   }
l.223 ...ratio]{author1.jpg}}]{Firstname Lastname}
                                                  
The requested image couldn't be read because
it was not a recognized image format.


d:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:223: Package graphics Error: Division by 0.

See the graphics package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.223 ...ratio]{author1.jpg}}]{Firstname Lastname}
                                                  
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.


d:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:223: Package graphics Error: Division by 0.

See the graphics package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.223 ...ratio]{author1.jpg}}]{Firstname Lastname}
                                                  
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.

File: author1.jpg Graphic file (type bmp)
<author1.jpg>

LaTeX Warning: File `author1.jpg' not found on input line 223.


d:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:223: Package graphics Error: Division by 0.

See the graphics package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.223 ...ratio]{author1.jpg}}]{Firstname Lastname}
                                                  
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.


d:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:223: Package graphics Error: Division by 0.

See the graphics package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.223 ...ratio]{author1.jpg}}]{Firstname Lastname}
                                                  
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.

File: author1.jpg Graphic file (type bmp)
<author1.jpg>

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 223.


LaTeX Warning: File `author2.jpg' not found on input line 227.

d:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:227: Unable to load picture or PDF file 'author2.jpg'.
<to be read again> 
                   }
l.227 ...atio]{author2.jpg}}]{Secondname Lastname}
                                                  
The requested image couldn't be read because
it was not a recognized image format.


d:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:227: Package graphics Error: Division by 0.

See the graphics package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.227 ...atio]{author2.jpg}}]{Secondname Lastname}
                                                  
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.


d:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:227: Package graphics Error: Division by 0.

See the graphics package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.227 ...atio]{author2.jpg}}]{Secondname Lastname}
                                                  
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.

File: author2.jpg Graphic file (type bmp)
<author2.jpg>

LaTeX Warning: File `author2.jpg' not found on input line 227.


d:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:227: Package graphics Error: Division by 0.

See the graphics package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.227 ...atio]{author2.jpg}}]{Secondname Lastname}
                                                  
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.


d:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:227: Package graphics Error: Division by 0.

See the graphics package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.227 ...atio]{author2.jpg}}]{Secondname Lastname}
                                                  
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.

File: author2.jpg Graphic file (type bmp)
<author2.jpg>

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 227.


LaTeX Warning: File `author3.jpg' not found on input line 231.

d:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:231: Unable to load picture or PDF file 'author3.jpg'.
<to be read again> 
                   }
l.231 ...ratio]{author3.jpg}}]{Thirdname Lastname}
                                                  
The requested image couldn't be read because
it was not a recognized image format.


d:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:231: Package graphics Error: Division by 0.

See the graphics package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.231 ...ratio]{author3.jpg}}]{Thirdname Lastname}
                                                  
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.


d:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:231: Package graphics Error: Division by 0.

See the graphics package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.231 ...ratio]{author3.jpg}}]{Thirdname Lastname}
                                                  
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.

File: author3.jpg Graphic file (type bmp)
<author3.jpg>

LaTeX Warning: File `author3.jpg' not found on input line 231.


d:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:231: Package graphics Error: Division by 0.

See the graphics package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.231 ...ratio]{author3.jpg}}]{Thirdname Lastname}
                                                  
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.


d:/deeplearn/detectron2/tgrs/ieee_tgrs_paper.tex:231: Package graphics Error: Division by 0.

See the graphics package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.231 ...ratio]{author3.jpg}}]{Thirdname Lastname}
                                                  
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.

File: author3.jpg Graphic file (type bmp)
<author3.jpg>

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 231.

[4] (ieee_tgrs_paper.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-01-04>
 ***********


LaTeX Font Warning: Some font shapes were not available, defaults substituted.


LaTeX Warning: There were undefined references.


LaTeX Warning: Label(s) may have changed. Rerun to get cross-references right.

 ) 
Here is how much of TeX's memory you used:
 11776 strings out of 409617
 180173 string characters out of 5778277
 1947191 words of memory out of 5000000
 33714 multiletter control sequences out of 15000+600000
 564872 words of font info for 74 fonts, out of 8000000 for 9000
 1351 hyphenation exceptions out of 8191
 72i,9n,79p,1288b,476s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on ieee_tgrs_paper.xdv (4 pages, 190280 bytes).
