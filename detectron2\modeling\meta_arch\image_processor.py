import torch
import torch.nn as nn
import torch.nn.functional as F
import matplotlib.pyplot as plt
from PIL import Image
import numpy as np
import torchvision.transforms as transforms

class ComplexConv2d(nn.Module):
    def __init__(self, in_channels, out_channels, kernel_size, stride=1, padding=0, device='cpu'):
        super(ComplexConv2d, self).__init__()
        self.device = device
        self.real_conv = nn.Conv2d(in_channels, out_channels, kernel_size, stride, padding).to(self.device)
        self.imag_conv = nn.Conv2d(in_channels, out_channels, kernel_size, stride, padding).to(self.device)

    def forward(self, x):
        if x.dtype != torch.complex64:
            x = torch.complex(x, torch.zeros_like(x).to(self.device))
        real_part = self.real_conv(torch.real(x))
        imag_part = self.imag_conv(torch.imag(x))
        return torch.complex(real_part, imag_part)

class LaplacianTransform(nn.Module):
    def __init__(self, in_channels, out_channels):
        super(LaplacianTransform, self).__init__()
        # 3x3 拉普拉斯算子
        laplacian_kernel_3x3 = torch.tensor([
            [1, 0, 1],
            [0, -4, 0],
            [1, 0, 1]
        ], dtype=torch.float32).unsqueeze(0).unsqueeze(0).repeat(in_channels, 1, 1, 1)

        # 5x5 拉普拉斯算子
        laplacian_kernel_5x5 = torch.tensor([
            [0, 0, 1, 0, 0],
            [0, 1, 2, 1, 0],
            [1, 2, -16, 2, 1],
            [0, 1, 2, 1, 0],
            [0, 0, 1, 0, 0]
        ], dtype=torch.float32).unsqueeze(0).unsqueeze(0).repeat(in_channels, 1, 1, 1)

        self.register_buffer('laplacian_3x3', laplacian_kernel_3x3)
        self.register_buffer('laplacian_5x5', laplacian_kernel_5x5)
        
        self.complex_conv = ComplexConv2d(in_channels, out_channels, kernel_size=3, padding=1)

    def forward(self, x):
        # 应用3x3拉普拉斯
        lap_3x3 = F.conv2d(x, self.laplacian_3x3, padding=1, groups=x.shape[1])
        # 应用5x5拉普拉斯
        lap_5x5 = F.conv2d(x, self.laplacian_5x5, padding=2, groups=x.shape[1])
        # 合并两种拉普拉斯结果
        laplacian_output = (lap_3x3 + lap_5x5) / 2
        # 应用复数卷积
        complex_output = self.complex_conv(laplacian_output)
        return torch.abs(complex_output)  # 返回幅度

class AdaptiveGaussianFilter(nn.Module):
    def __init__(self, in_channels, kernel_size=5, min_sigma=0.5, max_sigma=2.0):
        super(AdaptiveGaussianFilter, self).__init__()
        self.kernel_size = kernel_size
        self.min_sigma = min_sigma
        self.max_sigma = max_sigma
        self.in_channels = in_channels

    def _create_gaussian_kernel(self, sigma):
        # 创建高斯核
        x_coord = torch.arange(-(self.kernel_size//2), self.kernel_size//2 + 1, dtype=torch.float32)
        x_grid, y_grid = torch.meshgrid(x_coord, x_coord)
        gaussian_kernel = torch.exp(-(x_grid**2 + y_grid**2)/(2*sigma**2))
        gaussian_kernel = gaussian_kernel / gaussian_kernel.sum()
        return gaussian_kernel.unsqueeze(0).unsqueeze(0)

    def _compute_local_variance(self, x):
        # 计算局部方差
        mean = F.avg_pool2d(x, self.kernel_size, stride=1, padding=self.kernel_size//2)
        squared_mean = F.avg_pool2d(x**2, self.kernel_size, stride=1, padding=self.kernel_size//2)
        variance = squared_mean - mean**2
        return variance

    def forward(self, x):
        # 计算局部方差
        local_variance = self._compute_local_variance(x)
        
        # 根据局部方差自适应调整sigma
        sigma = self.min_sigma + (self.max_sigma - self.min_sigma) * torch.sigmoid(local_variance)
        
        # 对每个通道分别应用高斯滤波
        output = torch.zeros_like(x)
        for c in range(self.in_channels):
            channel_data = x[:, c:c+1]
            # 为每个位置创建适应性的高斯核
            for i in range(x.shape[2]):
                for j in range(x.shape[3]):
                    current_sigma = sigma[:, c:c+1, i, j]
                    kernel = self._create_gaussian_kernel(current_sigma.item())
                    kernel = kernel.to(x.device)
                    # 应用高斯滤波
                    output[:, c:c+1, i, j] = F.conv2d(
                        channel_data[..., max(0, i-self.kernel_size//2):min(x.shape[2], i+self.kernel_size//2+1),
                                   max(0, j-self.kernel_size//2):min(x.shape[3], j+self.kernel_size//2+1)],
                        kernel,
                        padding=0
                    )
        return output

class ResidualBlock(nn.Module):
    def __init__(self, in_channels, out_channels, stride=1):
        super(ResidualBlock, self).__init__()
        self.conv1 = nn.Conv2d(in_channels, out_channels, kernel_size=3, stride=stride, padding=1, bias=False)
        self.bn1 = nn.BatchNorm2d(out_channels)
        self.relu = nn.ReLU(inplace=True)
        self.conv2 = nn.Conv2d(out_channels, out_channels, kernel_size=3, stride=1, padding=1, bias=False)
        self.bn2 = nn.BatchNorm2d(out_channels)
        
        self.shortcut = nn.Sequential()
        if stride != 1 or in_channels != out_channels:
            self.shortcut = nn.Sequential(
                nn.Conv2d(in_channels, out_channels, kernel_size=1, stride=stride, bias=False),
                nn.BatchNorm2d(out_channels)
            )

    def forward(self, x):
        out = self.relu(self.bn1(self.conv1(x)))
        out = self.bn2(self.conv2(out))
        out += self.shortcut(x)
        return self.relu(out)

class FourierTransform(nn.Module):
    def __init__(self):
        super(FourierTransform, self).__init__()

    def forward(self, x):
        # 对输入进行2D FFT
        fft_result = torch.fft.fft2(x)
        
        # 获取实部和虚部
        real_part = torch.real(fft_result)
        imag_part = torch.imag(fft_result)
        
        # 将频谱移动到中心
        real_part = torch.fft.fftshift(real_part)
        imag_part = torch.fft.fftshift(imag_part)
        
        return real_part, imag_part

    def inverse(self, real_part, imag_part):
        # 重建复数张量
        complex_tensor = torch.complex(real_part, imag_part)
        # 将频谱从中心移回
        complex_tensor = torch.fft.ifftshift(complex_tensor)
        # 进行逆傅里叶变换
        return torch.abs(torch.fft.ifft2(complex_tensor))

class ImageProcessor(nn.Module):
    def __init__(self):
        super(ImageProcessor, self).__init__()
        self.fourier = FourierTransform()
        self.gaussian = AdaptiveGaussianFilter(in_channels=3)
        self.laplacian = LaplacianTransform(in_channels=3, out_channels=3)
        # 增加输入通道以适应傅里叶变换的实部和虚部
        self.residual1 = ResidualBlock(in_channels=15, out_channels=64, stride=1)
        self.residual2 = ResidualBlock(in_channels=64, out_channels=3, stride=1)

    def forward(self, x):
        # 应用傅里叶变换
        real_part, imag_part = self.fourier(x)
        # 应用自适应高斯滤波
        gaussian_features = self.gaussian(x)
        # 应用拉普拉斯变换
        lap_features = self.laplacian(x)
        # 拼接原始图像、傅里叶变换特征、高斯特征和拉普拉斯特征
        combined = torch.cat([x, real_part, imag_part, gaussian_features, lap_features], dim=1)
        out = self.residual1(combined)
        out = self.residual2(out)
        return out

def process_image(image_path, save_path=None, show_fourier=False):
    # 加载和预处理图像
    image = Image.open(image_path).convert('RGB')
    
    # 确保图像尺寸足够大
    min_size = 64  # 设置最小尺寸
    if image.size[0] < min_size or image.size[1] < min_size:
        # 将图像放大到最小尺寸
        ratio = min_size / min(image.size)
        new_size = (int(image.size[0] * ratio), int(image.size[1] * ratio))
        image = image.resize(new_size, Image.LANCZOS)
    
    transform = transforms.Compose([
        transforms.ToTensor(),
    ])
    input_tensor = transform(image).unsqueeze(0)
    
    # 初始化模型
    model = ImageProcessor()
    model.eval()
    
    # 获取傅里叶变换结果（用于可视化）
    fourier = FourierTransform()
    real_part, imag_part = fourier(input_tensor)
    
    # 处理图像
    with torch.no_grad():
        output = model(input_tensor)
    
    # 转换输出为图像格式
    output_image = output.squeeze(0).cpu()
    output_image = torch.clamp(output_image, 0, 1)
    output_image = transforms.ToPILImage()(output_image)
    
    if show_fourier:
        # 创建包含傅里叶变换结果的图像对比图
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 12))
        
        # 原始图像
        ax1.imshow(image)
        ax1.set_title('Original Image')
        ax1.axis('off')
        
        # 处理后的图像
        ax2.imshow(output_image)
        ax2.set_title('Processed Image')
        ax2.axis('off')
        
        # 傅里叶变换实部
        real_vis = torch.log(torch.abs(real_part) + 1).squeeze(0)[0]
        ax3.imshow(real_vis.cpu(), cmap='gray')
        ax3.set_title('Fourier Real Part (log scale)')
        ax3.axis('off')
        
        # 傅里叶变换虚部
        imag_vis = torch.log(torch.abs(imag_part) + 1).squeeze(0)[0]
        ax4.imshow(imag_vis.cpu(), cmap='gray')
        ax4.set_title('Fourier Imaginary Part (log scale)')
        ax4.axis('off')
    else:
        # 创建简单的图像对比图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(10, 5))
        ax1.imshow(image)
        ax1.set_title('Original Image')
        ax1.axis('off')
        
        ax2.imshow(output_image)
        ax2.set_title('Processed Image')
        ax2.axis('off')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path)
        plt.close()
    else:
        plt.show()
    
    return output_image

if __name__ == "__main__":
    # 示例使用
    # process_image("input_image.jpg", "output_comparison.png")
    print("请调用 process_image 函数并提供输入图像路径来处理图像")
