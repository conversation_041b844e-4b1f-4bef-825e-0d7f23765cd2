
Here are a few projects that are built on detectron2.
They are examples of how to use detectron2 as a library, to make your projects more
maintainable.

## Projects by Facebook

Note that these are research projects, and therefore may not have the same level
of support or stability as detectron2.

+ [DensePose: Dense Human Pose Estimation In The Wild](DensePose)
+ [Scale-Aware Trident Networks for Object Detection](TridentNet)
+ [TensorMask: A Foundation for Dense Object Segmentation](TensorMask)
+ [Mesh R-CNN](https://github.com/facebookresearch/meshrcnn)
+ [PointRend: Image Segmentation as Rendering](PointRend)
+ [Momentum Contrast for Unsupervised Visual Representation Learning](https://github.com/facebookresearch/moco/tree/master/detection)
+ [DETR: End-to-End Object Detection with Transformers](https://github.com/facebookresearch/detr/tree/master/d2)
+ [Panoptic-DeepLab: A Simple, Strong, and Fast Baseline for Bottom-Up Panoptic Segmentation](Panoptic-DeepLab)
+ [D2Go (Detectron2Go)](https://github.com/facebookresearch/d2go), an end-to-end production system for training and deployment for mobile platforms.
+ [Pointly-Supervised Instance Segmentation](PointSup)
+ [Unbiased Teacher for Semi-Supervised Object Detection](https://github.com/facebookresearch/unbiased-teacher)
+ [Rethinking "Batch" in BatchNorm](Rethinking-BatchNorm/)
+ [Per-Pixel Classification is Not All You Need for Semantic Segmentation](https://github.com/facebookresearch/MaskFormer)
+ [Exploring Plain Vision Transformer Backbones for Object Detection](ViTDet/)
+ [MViTv2: Improved Multiscale Vision Transformers for Classification and Detection](MViTv2/)


## External Projects

External projects in the community that use detectron2:

<!--
 - If you want to contribute, note that:
 -  1. please add your project to the list and try to use only one line
 -  2. the project must provide models trained on standard datasets

 Projects are *roughly sorted* by: "score = PaperCitation * 5 + Stars",
 where PaperCitation equals the citation count of the paper, if the project is an *official* implementation of the paper.
 PaperCitation equals 0 otherwise.
 -->

+ [AdelaiDet](https://github.com/aim-uofa/adet), a detection toolbox including FCOS, BlendMask, etc.
+ [CenterMask](https://github.com/youngwanLEE/centermask2)
+ [Res2Net backbones](https://github.com/Res2Net/Res2Net-detectron2)
+ [VoVNet backbones](https://github.com/youngwanLEE/vovnet-detectron2)
+ [FsDet](https://github.com/ucbdrive/few-shot-object-detection), Few-Shot Object Detection.
+ [Sparse R-CNN](https://github.com/PeizeSun/SparseR-CNN)
+ [BCNet](https://github.com/lkeab/BCNet), a bilayer decoupling instance segmentation method.
+ [DD3D](https://github.com/TRI-ML/dd3d), A fully convolutional 3D detector.
