MODEL:
  META_ARCHITECTURE: "GeneralizedRCNN"
  BACKBONE:
    NAME: "build_trident_resnet_backbone"
  ROI_HEADS:
    NAME: "TridentRes5ROIHeads"
    POSITIVE_FRACTION: 0.5
    BATCH_SIZE_PER_IMAGE: 128
    PROPOSAL_APPEND_GT: False
  PROPOSAL_GENERATOR:
    NAME: "TridentRPN"
  RPN:
    POST_NMS_TOPK_TRAIN: 500
  TRIDENT:
    NUM_BRANCH: 3
    BRANCH_DILATIONS: [1, 2, 3]
    TEST_BRANCH_IDX: 1
    TRIDENT_STAGE: "res4"
DATASETS:
  TRAIN: ("coco_2017_train",)
  TEST: ("coco_2017_val",)
SOLVER:
  IMS_PER_BATCH: 16
  BASE_LR: 0.02
  STEPS: (60000, 80000)
  MAX_ITER: 90000
INPUT:
  MIN_SIZE_TRAIN: (640, 672, 704, 736, 768, 800)
VERSION: 2
