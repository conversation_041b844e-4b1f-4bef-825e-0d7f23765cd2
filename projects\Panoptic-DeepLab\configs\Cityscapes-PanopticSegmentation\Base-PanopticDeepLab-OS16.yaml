MODEL:
  META_ARCHITECTURE: "PanopticDeepLab"
  BACKBONE:
    FREEZE_AT: 0
  RESNETS:
    OUT_FEATURES: ["res2", "res3", "res5"]
    RES5_DILATION: 2
  SEM_SEG_HEAD:
    NAME: "PanopticDeepLabSemSegHead"
    IN_FEATURES: ["res2", "res3", "res5"]
    PROJECT_FEATURES: ["res2", "res3"]
    PROJECT_CHANNELS: [32, 64]
    ASPP_CHANNELS: 256
    ASPP_DILATIONS: [6, 12, 18]
    ASPP_DROPOUT: 0.1
    HEAD_CHANNELS: 256
    CONVS_DIM: 256
    COMMON_STRIDE: 4
    NUM_CLASSES: 19
    LOSS_TYPE: "hard_pixel_mining"
    NORM: "SyncBN"
  INS_EMBED_HEAD:
    NAME: "PanopticDeepLabInsEmbedHead"
    IN_FEATURES: ["res2", "res3", "res5"]
    PROJECT_FEATURES: ["res2", "res3"]
    PROJECT_CHANNELS: [32, 64]
    ASPP_CHANNELS: 256
    ASPP_DILATIONS: [6, 12, 18]
    ASPP_DROPOUT: 0.1
    HEAD_CHANNELS: 32
    CONVS_DIM: 128
    COMMON_STRIDE: 4
    NORM: "SyncBN"
    CENTER_LOSS_WEIGHT: 200.0
    OFFSET_LOSS_WEIGHT: 0.01
  PANOPTIC_DEEPLAB:
    STUFF_AREA: 2048
    CENTER_THRESHOLD: 0.1
    NMS_KERNEL: 7
    TOP_K_INSTANCE: 200
DATASETS:
  TRAIN: ("cityscapes_fine_panoptic_train",)
  TEST: ("cityscapes_fine_panoptic_val",)
SOLVER:
  OPTIMIZER: "ADAM"
  BASE_LR: 0.001
  WEIGHT_DECAY: 0.0
  WEIGHT_DECAY_NORM: 0.0
  WEIGHT_DECAY_BIAS: 0.0
  MAX_ITER: 60000
  LR_SCHEDULER_NAME: "WarmupPolyLR"
  IMS_PER_BATCH: 32
INPUT:
  MIN_SIZE_TRAIN: (512, 640, 704, 832, 896, 1024, 1152, 1216, 1344, 1408, 1536, 1664, 1728, 1856, 1920, 2048)
  MIN_SIZE_TRAIN_SAMPLING: "choice"
  MIN_SIZE_TEST: 1024
  MAX_SIZE_TRAIN: 4096
  MAX_SIZE_TEST: 2048
  CROP:
    ENABLED: True
    TYPE: "absolute"
    SIZE: (1024, 2048)
DATALOADER:
  NUM_WORKERS: 10
VERSION: 2
