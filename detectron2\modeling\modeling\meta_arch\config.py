from detectron2.config import CfgNode as CN

def add_physics_cl_config(cfg):
    """
    添加物理信息引导增量学习模块的配置项
    
    Args:
        cfg (CN): 配置节点
    """
    _C = cfg
    
    # 添加物理信息引导增量学习模块的配置
    _C.MODEL.PHYSICS_CL = CN()
    _C.MODEL.PHYSICS_CL.ENABLED = True  # 是否启用物理信息引导增量学习模块
    _C.MODEL.PHYSICS_CL.MEMORY_SIZE = 200  # 内存大小
    _C.MODEL.PHYSICS_CL.INITIAL_A = 0.01  # 初始记忆因子
    _C.MODEL.PHYSICS_CL.MAX_A = 0.5  # 最大记忆因子
    _C.MODEL.PHYSICS_CL.DIFF_COEFF = 0.1  # 扩散系数
    _C.MODEL.PHYSICS_CL.PHYSICS_WEIGHT = 0.1  # 物理损失权重
    _C.MODEL.PHYSICS_CL.PDE_TYPE = "diffusion"  # 物理方程类型 ["diffusion", "reaction-diffusion", "wave"]
    
    # 优化器配置
    _C.SOLVER.PHYSICS_CL_LR = 0.0001  # 物理信息引导增量学习模块的学习率
    
    return _C 