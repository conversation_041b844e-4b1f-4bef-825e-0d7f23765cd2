[isort]
line_length=100
multi_line_output=3
include_trailing_comma=True
known_standard_library=numpy,setuptools,mock
skip=./datasets,docs
skip_glob=*/__init__.py,**/configs/**,**/tests/config/**
known_myself=detectron2
known_third_party=fvcore,matplotlib,cv2,torch,torchvision,PIL,pycocotools,yacs,termcolor,cityscapesscripts,tabulate,tqdm,scipy,lvis,psutil,pkg_resources,caffe2,onnx,panopticapi,black,isort,av,iopath,omegaconf,hydra,yaml,pydoc,submitit,cloudpickle,packaging
no_lines_before=STDLIB,THIRDPARTY
sections=FUTURE,STDL<PERSON>,THIRDPARTY,myself,FIRSTPARTY,LOCALFOLDER
default_section=FIRSTPARTY

[mypy]
python_version=3.7
ignore_missing_imports = True
warn_unused_configs = True
disallow_untyped_defs = True
check_untyped_defs = True
warn_unused_ignores = True
warn_redundant_casts = True
show_column_numbers = True
follow_imports = silent
allow_redefinition = True
; Require all functions to be annotated
disallow_incomplete_defs = True
