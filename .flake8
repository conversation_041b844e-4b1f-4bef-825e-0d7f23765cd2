# This is an example .flake8 config, used when developing *Black* itself.
# Keep in sync with setup.cfg which is used for source packages.

[flake8]
ignore = W503, E203, E221, C901, C408, E741, C407, B017, F811, C101, EXE001, EXE002
max-line-length = 100
max-complexity = 18
select = B,C,E,F,W,T4,B9
exclude = build
per-file-ignores =
  **/__init__.py:F401,F403,E402
  **/configs/**.py:F401,E402
  configs/**.py:F401,E402
  **/tests/config/**.py:F401,E402
  tests/config/**.py:F401,E402
