import torch
import torch.nn as nn
import torch.nn.functional as F
import math


class MambaBlock(nn.Module):
    """Mamba区块，基于SSM (状态空间模型) 机制处理图像特征"""
    
    def __init__(self, d_model, d_state=16, d_conv=4, expand=2):
        super().__init__()
        self.d_model = d_model
        self.d_state = d_state
        self.d_conv = d_conv
        self.expand = expand
        self.d_inner = int(expand * d_model)
        
        # 投影层
        self.in_proj = nn.Linear(d_model, self.d_inner * 2)
        
        # 一维卷积，用于局部信息捕获
        self.conv1d = nn.Conv1d(
            in_channels=self.d_inner,
            out_channels=self.d_inner,
            kernel_size=d_conv,
            padding=d_conv - 1,
            groups=self.d_inner
        )
        
        # S4参数
        self.A_log = nn.Parameter(torch.randn(self.d_inner, self.d_state))
        self.D = nn.Parameter(torch.randn(self.d_inner))
        self.B = nn.Parameter(torch.randn(self.d_inner, self.d_state))
        self.C = nn.Parameter(torch.randn(self.d_inner, self.d_state))
        
        # 输出投影
        self.out_proj = nn.Linear(self.d_inner, d_model)
        
        # 初始化
        self._init_weights()
    
    def _init_weights(self):
        nn.init.normal_(self.A_log, mean=0.0, std=0.1)
        nn.init.normal_(self.B, mean=0.0, std=0.1)
        nn.init.normal_(self.C, mean=0.0, std=0.1)
        nn.init.normal_(self.D, mean=0.0, std=0.1)
        
        nn.init.xavier_uniform_(self.in_proj.weight)
        nn.init.zeros_(self.in_proj.bias)
        nn.init.xavier_uniform_(self.out_proj.weight)
        nn.init.zeros_(self.out_proj.bias)
        
        nn.init.xavier_uniform_(self.conv1d.weight)
        nn.init.zeros_(self.conv1d.bias)
    
    def forward(self, x):
        """
        前向传播过程
        
        Args:
            x: 输入张量 [B, L, D]
            
        Returns:
            x: 输出张量 [B, L, D]
        """
        B, L, _ = x.shape
        
        # 投影至内部维度
        x_proj = self.in_proj(x)  # [B, L, 2*D_inner]
        x_proj_1, x_proj_2 = x_proj.chunk(2, dim=-1)  # 两部分: [B, L, D_inner]
        
        # 应用门控机制
        x_proj_1 = x_proj_1.permute(0, 2, 1)  # [B, D_inner, L]
        x_conv = self.conv1d(x_proj_1)[..., :L]  # [B, D_inner, L]
        x_conv = x_conv.permute(0, 2, 1)  # [B, L, D_inner]
        
        # 计算S4部分
        A = -torch.exp(self.A_log)  # [D_inner, D_state]
        
        # 获取特征的投影
        x_proj_2 = F.silu(x_proj_2)
        
        # 序列处理近似实现 (简化版本)
        # 在实际的Mamba实现中，这是通过更高效的并行算法计算的
        hidden = torch.zeros(B, self.d_inner, self.d_state, device=x.device)
        ys = []
        
        for i in range(L):
            # 更新隐藏状态
            u = x_conv[:, i, :]  # [B, D_inner]
            hidden = hidden + u.unsqueeze(-1) * self.B.unsqueeze(0)  # [B, D_inner, D_state]
            
            # 输出投影
            y = torch.einsum('bds,ds->bd', hidden, self.C) + u * self.D  # [B, D_inner]
            ys.append(y)
            
            # 应用状态转换
            hidden = hidden * A.unsqueeze(0)
        
        # 组合所有时间步的输出
        y = torch.stack(ys, dim=1)  # [B, L, D_inner]
        
        # 门控机制
        y = y * x_proj_2
        
        # 输出投影
        y = self.out_proj(y)  # [B, L, D]
        
        return y


class MambaFeatureProcessor(nn.Module):
    """
    Mamba特征处理器，用于处理2D图像特征
    """
    
    def __init__(self, d_model, d_state=16, d_conv=4, expand=2, depth=2):
        super().__init__()
        self.d_model = d_model
        self.depth = depth
        
        # 标准化层
        self.norm_layers = nn.ModuleList([
            nn.LayerNorm(d_model) for _ in range(depth)
        ])
        
        # Mamba区块
        self.mamba_blocks = nn.ModuleList([
            MambaBlock(d_model, d_state, d_conv, expand) for _ in range(depth)
        ])
        
        # 特征融合
        self.fusion = nn.Sequential(
            nn.Conv2d(d_model, d_model, kernel_size=1),
            nn.BatchNorm2d(d_model),
            nn.SiLU(),
            nn.Conv2d(d_model, d_model, kernel_size=1)
        )
    
    def forward(self, x):
        """
        前向传播
        
        Args:
            x: 图像特征 [B, C, H, W]
            
        Returns:
            enhanced_features: 增强特征 [B, C, H, W]
        """
        B, C, H, W = x.shape
        identity = x
        
        # 重塑为序列处理
        x_seq = x.flatten(2).permute(0, 2, 1)  # [B, H*W, C]
        
        # 应用多层Mamba块
        for i in range(self.depth):
            residual = x_seq
            x_seq = self.norm_layers[i](x_seq)
            x_seq = self.mamba_blocks[i](x_seq)
            x_seq = residual + x_seq
        
        # 重塑回2D特征图
        x_out = x_seq.permute(0, 2, 1).reshape(B, C, H, W)
        
        # 应用特征融合
        enhanced_features = self.fusion(x_out) + identity
        
        return enhanced_features 