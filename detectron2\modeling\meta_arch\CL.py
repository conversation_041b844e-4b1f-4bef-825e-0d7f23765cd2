# CL.py
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from .clresnet import resnet1, resnet2, resnet3, resnet4, resnet5
from .pinn import PINN  # 导入 PINN 模块


class MemoryBuffer:
    def __init__(self, max_size=100, device='cpu'):
        self.buffer = []
        self.max_size = max_size
        self.device = device

    def add(self, feature):
        """将新特征添加到内存缓冲区，避免保留计算图。"""
        if not isinstance(feature, dict):
            raise ValueError("特征必须是字典")
        if len(self.buffer) >= self.max_size:
            self.buffer.pop(0)  # 删除最早的特征
        # 将特征移动到设备上，不保留计算图
        feature_on_device = {k: v.detach().to(self.device) for k, v in feature.items()}
        self.buffer.append(feature_on_device)

    def sample(self, num_samples):
        """从内存缓冲区中取固定数量的最早添加的特征。"""
        if len(self.buffer) == 0:
            return []  # 如果缓冲区为空，返回空列表
        num_samples = min(num_samples, len(self.buffer))
        return self.buffer[:num_samples]  # 取最早添加的 num_samples 个特征


class iCaRLNet(nn.Module):
    def __init__(self, feature_sizes, device='cpu', initial_a=0.01, max_a=1.0):
        super(iCaRLNet, self).__init__()
        self.device = device
        self.a = initial_a  # 初始因子 a
        self.max_a = max_a  # 最大因子 a

        # 特征提取器初始化
        self.feature_extractors = nn.ModuleDict({
            key: self._get_feature_extractor(key)
            for key in feature_sizes.keys()
        })

        # 批归一化层
        self.bns = nn.ModuleDict({
            key: nn.BatchNorm2d(512, momentum=0.01)
            for key in feature_sizes.keys()
        })
        self.bn3 = nn.BatchNorm2d(256)

        # ReLU 层
        self.relus = nn.ModuleDict({key: nn.ReLU() for key in feature_sizes.keys()})

        # 卷积层用于特征维度降维
        self.conv = nn.Conv2d(512, 256, kernel_size=1).to(device)

        # PINN 模块
        self.pinn = PINN().to(device)

        # 内存缓冲区用于存储旧特征
        self.memory_buffer = MemoryBuffer(max_size=100, device=device)

        # 将所有模块移动到设备
        self.to(device)

    def _get_feature_extractor(self, key):
        """根据特征键初始化相应的 ResNet"""
        resnet_dict = {
            'p2': resnet1,
            'p3': resnet2,
            'p4': resnet3,
            'p5': resnet4,
            'p6': resnet5,
        }
        if key not in resnet_dict:
            raise ValueError(f"未知的特征键: {key}")
        resnet = resnet_dict[key]().to(self.device)
        resnet.fc = nn.Identity()  # 移除最后一层全连接层
        return resnet

    def forward(self, features, update_memory=True, use_old_features=True):
        processed_features = {}
        pinn_features = {}
        for key, x in features.items():
            feature_extractor = self.feature_extractors[key]
            bn = self.bns[key]
            relu = self.relus[key]

            # 特征提取和归一化（避免对非可训练层计算梯度）
            with torch.no_grad():
                output = feature_extractor(x)
                output = bn(output)
                output = relu(output)

            # 使用卷积和上采样进行维度降维
            reduced_features = self.conv(output)
            upsampled_features = F.interpolate(reduced_features, size=(x.shape[2], x.shape[3]), mode='bilinear',
                                               align_corners=False)
            processed_features[key] = upsampled_features
            pinn_features[key] = self.pinn.inference(processed_features[key])
        
        # 获取 PINN 特征
        

        # 根据标志决定是否使用旧特征
        if use_old_features:
            # 计算要抽取的旧特征数量
            num_samples = int(100 * self.a)
            # 从内存中取固定数量的最早添加的旧特征
            old_features = self.sample_from_memory(num_samples=num_samples)
        else:
            old_features = []

        # 特征融合
        fused_features = self._fuse_features(processed_features, pinn_features, old_features)

        # 可选：使用融合特征更新内存
        if update_memory:
            self.update_memory_buffer(fused_features)

        return fused_features

    def _fuse_features(self, processed_features, pinn_features, old_features):
        fused_features = {}
        for key in processed_features.keys():
            x_icarl = processed_features[key]
            x_pinn = pinn_features[key]

            # 将 PINN 特征调整为目标形状
            C = self._pad_to_target_shape(x_pinn, x_icarl)

            # 如果有旧特征，融合旧特征
            if old_features:
                for x_old in old_features:
                    x_old = x_old[key]
                    x_old = x_old.to(C.device)  # 确保旧特征在同一个设备上

                    x_old = F.interpolate(x_old, size=(C.shape[2], C.shape[3]), mode='bilinear', align_corners=False)
                    C += x_old  # 对旧特征乘以0.01

            # 执行加权融合
            fused_features[key] = (x_icarl + C) / (2 + len(old_features))  # 平均融合（可以调整）

        return fused_features

    def _pad_to_target_shape(self, x_pinn, x_icarl):
        b, c, h, w = x_icarl.shape
        target_elements = b * c * h * w
        current_elements = x_pinn.numel()
        padding_elements = target_elements - current_elements

        # 如果需要，应用填充
        if padding_elements > 0:
            padding = torch.zeros(padding_elements, 1).to(x_pinn.device)
            x_pinn_flattened = torch.cat([x_pinn.view(-1, 1), padding], dim=0)
        else:
            x_pinn_flattened = x_pinn.view(-1, 1)

        C = torch.zeros(target_elements, 1).to(x_pinn.device)
        C[:x_pinn_flattened.shape[0]] = x_pinn_flattened.view(-1, 1)
        return C.view(b, c, h, w)

    def freeze_old_layers(self):
        """冻结旧层的参数"""
        for key in self.feature_extractors.keys():
            for param in self.feature_extractors[key].parameters():
                param.requires_grad = False
            for param in self.bns[key].parameters():
                param.requires_grad = False
            for param in self.relus[key].parameters():
                param.requires_grad = False

    def update_memory_buffer(self, features):
        """使用新特征更新内存缓冲区"""
        features_cpu = {k: v.cpu() for k, v in features.items()}
        self.memory_buffer.add(features_cpu)

    def sample_from_memory(self, num_samples):
        """从内存缓冲区中取固定数量的最早添加的特征"""
        return self.memory_buffer.sample(num_samples)

    def update_factor_a(self, a):
        """更新因子 a"""
        self.a = min(max(a, 0.0), self.max_a)
