_BASE_: "../Base-DensePose-RCNN-FPN.yaml"
MODEL:
  WEIGHTS: "detectron2://ImageNetPretrained/MSRA/R-50.pkl"
  RESNETS:
    DEPTH: 50
  ROI_DENSEPOSE_HEAD:
    UV_CONFIDENCE:
      ENABLED: True
      TYPE: "indep_aniso"
    POINT_REGRESSION_WEIGHTS: 0.0005
DATASETS:
  TRAIN: ("densepose_coco_2014_minival_100",)
  TEST: ("densepose_coco_2014_minival_100",)
SOLVER:
  CLIP_GRADIENTS:
    ENABLED: True
  MAX_ITER: 40 
  STEPS: (30,)
  WARMUP_FACTOR: 0.025
