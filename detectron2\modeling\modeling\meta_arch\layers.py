# layers.py
import torch
from torch.autograd import Function
import numpy as np
from .quantum_circuit import QuanConvCircuit

class QuanConvFunction(Function):
    @staticmethod
    def forward(ctx, inputs, in_channels, out_channels, kernel_size, quantum_circuits, shift):
        ctx.in_channels = in_channels
        ctx.out_channels = out_channels
        ctx.kernel_size = kernel_size
        ctx.quantum_circuits = quantum_circuits
        ctx.shift = shift

        batch_size, _, len_x, len_y = inputs.size()
        len_x = len_x - kernel_size + 1
        len_y = len_y - kernel_size + 1

        device = inputs.device
        outputs = torch.zeros((batch_size, out_channels, len_x, len_y), device=device)

        for i in range(batch_size):
            input = inputs[i]
            for c in range(out_channels):
                circuit = quantum_circuits[c]
                patches = input.unfold(1, kernel_size, 1).unfold(2, kernel_size, 1)
                patches = patches.contiguous().view(in_channels, len_x, len_y, kernel_size, kernel_size)
                results = torch.zeros((len_x, len_y), device=device)
                for h in range(len_y):
                    for w in range(len_x):
                        data = patches[:, h, w]
                        result = circuit.run(data)
                        results[h, w] = result
                outputs[i, c] = results

        ctx.save_for_backward(inputs, outputs)
        return outputs

    @staticmethod
    def backward(ctx, grad_outputs):
        inputs, outputs = ctx.saved_tensors
        grad_inputs = torch.zeros_like(inputs)

        device = inputs.device

        for i in range(len(inputs)):
            for c in range(ctx.out_channels):
                circuit = ctx.quantum_circuits[c]
                for h in range(outputs.size(2)):
                    for w in range(outputs.size(3)):
                        data = inputs[i, :, h:h + ctx.kernel_size, w:w + ctx.kernel_size]
                        grad_output_patch = grad_outputs[i, c, h, w].item()

                        data_right = data + ctx.shift
                        data_left = data - ctx.shift

                        expectation_right = circuit.run(data_right)
                        expectation_left = circuit.run(data_left)

                        gradient = (expectation_right - expectation_left) / (2 * ctx.shift)
                        grad_inputs[i, :, h:h + ctx.kernel_size, w:w + ctx.kernel_size] += gradient * grad_output_patch

        return grad_inputs.float(), None, None, None, None, None


class QuanConv(torch.nn.Module):
    def __init__(self, in_channels, out_channels=1, kernel_size=2, n_qubits=4, shots=1, shift=np.pi / 2, device="lightning.gpu"):
        """量子卷积层初始化

        Args:
            in_channels (int): 输入图像的通道数
            out_channels (int, optional): 卷积后输出的通道数，默认是1
            kernel_size (int, optional): 卷积核的大小，默认是2
            n_qubits (int, optional): 每个通道使用的量子比特数，默认是4
            shots (int, optional): 量子电路执行的shots数，默认是1
            shift (float, optional): 参数偏移规则的偏移量，默认是 `np.pi / 2`
            device (str, optional): PennyLane 设备，默认是 "lightning.gpu"
        """
        super(QuanConv, self).__init__()
        self.quantum_circuits = [QuanConvCircuit(
            n_qubits=n_qubits, shots=shots, threshold=127, device=device) for _ in range(out_channels)]
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.kernel_size = kernel_size
        self.n_qubits = n_qubits
        self.shift = shift

    def forward(self, inputs):
        """定义前向传播

        Args:
            inputs (Tensor): 输入张量
        """
        return QuanConvFunction.apply(inputs, self.in_channels, self.out_channels, self.kernel_size,
                                      self.quantum_circuits, self.shift)
