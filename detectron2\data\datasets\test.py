from detectron2.data import DatasetCatalog, MetadataCatalog

# 使用注册时的名称进行检查
dataset_name_train = "coco_my_train"  # 你注册时用的名字
dataset_name_val = "coco_my_val"  # 你注册时用的名字

# 检查训练集是否注册
if dataset_name_train in DatasetCatalog:
    print(f"Dataset {dataset_name_train} is registered!")
else:
    print(f"Dataset {dataset_name_train} is not registered.")

# 检查验证集是否注册
if dataset_name_val in MetadataCatalog:
    print(f"Metadata for {dataset_name_val} is registered!")
else:
    print(f"Metadata for {dataset_name_val} is not registered.")
