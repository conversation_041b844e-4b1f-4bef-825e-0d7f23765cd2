import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np


class Swish(nn.Module):
    def __init__(self, inplace=True):
        super(Swish, self).__init__()
        self.inplace = inplace

    def forward(self, x):
        if self.inplace:
            x.mul_(torch.sigmoid(x))
            return x
        else:
            return x * torch.sigmoid(x)


class Net2(nn.Module):
    def __init__(self, input_n=2, h_n=10):
        super(Net2, self).__init__()

        self.main = nn.Sequential(
            nn.Linear(input_n, h_n),
            Swish(),
            nn.Linear(h_n, h_n),
            Swish(),
            nn.Linear(h_n, h_n),
            Swish(),
            nn.Linear(h_n, 1),
        )

    def forward(self, x):
        output = self.main(x)
        return output


class PINN(nn.Module):
    def __init__(self, input_n=2, h_nD=30, h_n=10, Diff=0.1, f_source=-1.0, Lambda_BC=0.001):
        super(PINN, self).__init__()
        self.Diff = Diff
        self.f_source = f_source
        self.Lambda_BC = Lambda_BC

        self.net2 = Net2(input_n=input_n, h_n=h_n)

    def forward(self, x, y):
        # 确保输入张量与模型参数在同一个设备上
        device = next(self.parameters()).device
        x, y = x.to(device), y.to(device)
        net_in = torch.cat((x, y), 1)
        C = self.net2(net_in)
        C = C.view(len(C), -1)
        return C

    def criterion(self, x, y):
        # 确保输入张量与模型参数在同一个设备上
        device = next(self.parameters()).device
        x, y = x.to(device), y.to(device)
        x.requires_grad = True
        y.requires_grad = True
        net_in = torch.cat((x, y), 1)
        C = self.net2(net_in)
        C = C.view(len(C), -1)

        # 计算一阶导数
        c_x = torch.autograd.grad(C, x, grad_outputs=torch.ones_like(x), create_graph=True, only_inputs=True)[0]
        c_y = torch.autograd.grad(C, y, grad_outputs=torch.ones_like(y), create_graph=True, only_inputs=True)[0]
        
        # 计算二阶导数
        c_xx = torch.autograd.grad(c_x, x, grad_outputs=torch.ones_like(x), create_graph=True, only_inputs=True)[0]
        c_yy = torch.autograd.grad(c_y, y, grad_outputs=torch.ones_like(y), create_graph=True, only_inputs=True)[0]
        
        # 泊松方程: -∇²u = f，其中 ∇²u = c_xx + c_yy
        # 泊松方程残差: f + ∇²u = f + (c_xx + c_yy)
        poisson_residual = self.f_source + self.Diff * (c_xx + c_yy)
        
        # 计算残差的MSE损失
        loss_f = nn.MSELoss()
        loss_pde = loss_f(poisson_residual, torch.zeros_like(poisson_residual))
        
        # 添加梯度正则化项，促使解更平滑
        grad_penalty = torch.mean(c_x**2 + c_y**2)
        
        # 总损失 = PDE损失 + 梯度正则化
        loss = loss_pde + 0.01 * grad_penalty
        
        return loss

    def Loss_BC(self, xb, yb, cb):
        device = next(self.parameters()).device
        # 移除不必要的类型转换
        xb = xb.to(device)
        yb = yb.to(device)
        cb = cb.to(device)

        net_in = torch.cat((xb, yb), 1)
        out = self.net2(net_in)
        c_bc = out.view(len(out), -1)

        loss_f = nn.MSELoss()
        loss_bc_Dirichlet = loss_f(c_bc, cb)
        return loss_bc_Dirichlet

    def inference(self, feature):
        device = next(self.parameters()).device
        # 假设 feature 是一个单个特征图，形状为 (batch_size, channels, height, width)
        b = feature.shape[0]
        c = feature.shape[1]
        h = feature.shape[2]
        w = feature.shape[3]
        feature = F.interpolate(feature, size=(160, 160), mode='bilinear', align_corners=False)
        nPt = feature.shape[2] * feature.shape[3]  # 计算特征图中的元素数量
        xStart = 0.
        xEnd = 1.
        yStart = 0.
        yEnd = 1.0

        # 生成 x 和 y 的坐标
        x = np.linspace(xStart, xEnd, feature.shape[2])  # 对应 feature 的宽度
        y = np.linspace(yStart, yEnd, feature.shape[3])  # 对应 feature 的高度
        x, y = np.meshgrid(x, y)
        x = np.reshape(x, (np.size(x[:]), 1))
        y = np.reshape(y, (np.size(y[:]), 1))

        x = torch.tensor(x, dtype=torch.float32, device=device)
        y = torch.tensor(y, dtype=torch.float32, device=device)

        # 进行预测
        C = self.forward(x, y)  # 计算预测结果

        target_shape = (b * c * h * w, 1)

        # 创建一个新的零矩阵，大小为目标形状
        C = torch.zeros(*target_shape, device=device)

        # 将原始矩阵 C 放到零矩阵的左上角
        C[:C.shape[0], :C.shape[1]] = C
        C = C.view(b, c, h, w)
        return C
