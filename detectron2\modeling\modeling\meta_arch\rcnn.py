import logging
import numpy as np
from typing import Dict, List, Optional, Tuple
import torch
from torch import nn

from detectron2.config import configurable
from detectron2.data.detection_utils import convert_image_to_rgb
from detectron2.layers import move_device_like
from detectron2.structures import ImageList, Instances
from detectron2.utils.events import get_event_storage
from detectron2.utils.logger import log_first_n
import torch.nn.functional as F
from ..backbone import Backbone, build_backbone
from .models import Net
from .CL import iCaRLNet
from .pinn import PINN  # 导入 PINN 模块
from ..postprocessing import detector_postprocess
from ..proposal_generator import build_proposal_generator
from ..roi_heads import build_roi_heads
from .build import META_ARCH_REGISTRY

__all__ = ["GeneralizedRCNN", "ProposalNetwork"]

import random
from collections import defaultdict

class QLearningAgent:
    def __init__(self, state_space, action_space, alpha=0.1, gamma=0.9, epsilon=0.1):
        self.q_table = np.zeros((state_space, action_space))
        self.alpha = alpha  # 学习率
        self.gamma = gamma  # 折扣因子
        self.epsilon = epsilon  # 探索率
        self.action_space = action_space  # 动作空间

    def update(self, state, action, reward, next_state):
        # Q-learning 更新规则
        best_next_action = np.argmax(self.q_table[next_state])
        self.q_table[state, action] = (1 - self.alpha) * self.q_table[state, action] + \
                                      self.alpha * (reward + self.gamma * self.q_table[next_state, best_next_action])

    def select_action(self, state):
        # epsilon-greedy 策略
        if np.random.rand() < self.epsilon:
            return np.random.choice(self.action_space)  # 探索
        else:
            return np.argmax(self.q_table[state])  # 利用


class GeneticAlgorithm:
    def __init__(self, population_size, mutation_rate, max_generations):
        self.population_size = population_size
        self.mutation_rate = mutation_rate
        self.max_generations = max_generations

    def fitness(self, a, loss_history):
        """根据模型的损失和精度来计算适应度"""
        return -np.mean(loss_history[-10:])  # 使用最近10次的平均损失作为适应度

    def select_parents(self, population, loss_history):
        """选择适应度较好的父代"""
        sorted_population = sorted(population, key=lambda a: self.fitness(a, loss_history), reverse=True)
        return sorted_population[:2]  # 选择最适应的两个

    def crossover(self, parent1, parent2):
        """交叉操作"""
        offspring1 = (parent1 + parent2) / 2  # 简单的交叉方式
        offspring2 = (parent1 + parent2) / 2 + np.random.uniform(-0.1, 0.1)  # 添加一些随机性
        return [offspring1, offspring2]  # 返回一个包含两个后代的列表

    def mutate(self, a):
        """变异操作"""
        a = a + np.random.uniform(-self.mutation_rate, self.mutation_rate)
        return max(0, min(a, 0.1))

    def optimize(self, initial_population, loss_history):
        population = initial_population
        for generation in range(self.max_generations):
            parents = self.select_parents(population, loss_history)
            offspring = self.crossover(parents[0], parents[1])
            population = [self.mutate(a) for a in offspring]
        return population[0]  # 返回最优解


class SimulatedAnnealing:
    def __init__(self, initial_a, max_temp, min_temp, cooling_rate):
        self.a = initial_a
        self.max_temp = max_temp
        self.min_temp = min_temp
        self.cooling_rate = cooling_rate

    def acceptance_probability(self, old_cost, new_cost, temperature):
        """根据温度和成本差异计算接受概率"""
        if new_cost < old_cost:
            return 1.0
        return np.exp((old_cost - new_cost) / temperature)

    def optimize(self, loss_history):
        temperature = self.max_temp
        while temperature > self.min_temp:
            # 在当前解附近生成新解
            new_a = self.a + np.random.uniform(-0.1, 0.1)
            old_cost = self.evaluate(self.a, loss_history)
            new_cost = self.evaluate(new_a, loss_history)

            # 根据接受概率判断是否接受新解
            if np.random.rand() < self.acceptance_probability(old_cost, new_cost, temperature):
                self.a = new_a

            # 降低温度
            temperature *= self.cooling_rate
        return self.a

    def evaluate(self, a, loss_history):
        """评估因子 a 的效果，例如通过模型的损失函数"""
        return np.mean(loss_history[-10:])  # 使用最近10次的平均损失作为评估


@META_ARCH_REGISTRY.register()
class GeneralizedRCNN(nn.Module):
    """
    Generalized R-CNN. Any models that contains the following three components:
    1. Per-image feature extraction (aka backbone)
    2. Region proposal generation
    3. Per-region feature extraction and prediction
    """

    @configurable
    def __init__(
            self,
            *,
            backbone: Backbone,
            proposal_generator: nn.Module,
            roi_heads: nn.Module,
            net: nn.Module,
            icalnet: iCaRLNet,
            pinn: nn.Module,  # 添加 PINN 模块
            pixel_mean: Tuple[float],
            pixel_std: Tuple[float],
            input_format: Optional[str] = None,
            vis_period: int = 0,
    ):
        """
        Args:
            backbone: a backbone module, must follow detectron2's backbone interface
            proposal_generator: a module that generates proposals using backbone features
            roi_heads: a ROI head that performs per-region computation
            net: a module that processes images to generate feature maps
            icalnet: a module that processes feature maps
            pixel_mean, pixel_std: list or tuple with #channels element, representing
                the per-channel mean and std to be used to normalize the input image
            input_format: describe the meaning of channels of input. Needed by visualization
            vis_period: the period to run visualization. Set to 0 to disable.
        """
        super().__init__()
        self.backbone = backbone
        self.proposal_generator = proposal_generator
        self.roi_heads = roi_heads
        self.net = net
        self.icalnet = icalnet
        self.pinn = pinn
        self.input_format = input_format
        self.vis_period = vis_period
        if vis_period > 0:
            assert input_format is not None, "input_format is required for visualization!"

        self.register_buffer("pixel_mean", torch.tensor(pixel_mean).view(-1, 1, 1), False)
        self.register_buffer("pixel_std", torch.tensor(pixel_std).view(-1, 1, 1), False)
        assert (
                self.pixel_mean.shape == self.pixel_std.shape
        ), f"{self.pixel_mean} and {self.pixel_std} have different shapes!"

        # 混合智能优化相关参数
        self.q_learning_agent = QLearningAgent(state_space=20, action_space=3, alpha=0.1, gamma=0.9, epsilon=0.1)
        self.genetic_algorithm = GeneticAlgorithm(population_size=20, mutation_rate=0.1, max_generations=10)
        self.simulated_annealing = SimulatedAnnealing(initial_a=0.01, max_temp=100, min_temp=1, cooling_rate=0.95)
        self.optimization_stage = "initial"  # 初始阶段使用模拟退火，中间阶段使用强化学习，最后阶段使用遗传算法
        self.step_count = 0
        self.a_current = 0.01
        self.loss_history = []  # 用于存储损失历史

    @classmethod
    def from_config(cls, cfg):
        backbone = build_backbone(cfg)
        return {
            "backbone": backbone,
            "proposal_generator": build_proposal_generator(cfg, backbone.output_shape()),
            "roi_heads": build_roi_heads(cfg, backbone.output_shape()),
            "net": Net(),  # 添加 Net 模块
            "icalnet": iCaRLNet(feature_sizes={'p2': 256, 'p3': 256, 'p4': 256, 'p5': 256, 'p6': 256}),
            # 添加 iCaRLNet 模块
            "pinn": PINN(),  # 添加 PINN 模块
            "input_format": cfg.INPUT.FORMAT,
            "vis_period": cfg.VIS_PERIOD,
            "pixel_mean": cfg.MODEL.PIXEL_MEAN,
            "pixel_std": cfg.MODEL.PIXEL_STD,
        }

    @property
    def device(self):
        return self.pixel_mean.device

    def _move_to_current_device(self, x):
        return move_device_like(x, self.pixel_mean)

    def visualize_training(self, batched_inputs, proposals):
        """
        A function used to visualize images and proposals. It shows ground truth
        bounding boxes on the original image and up to 20 top-scoring predicted
        object proposals on the original image. Users can implement different
        visualization functions for different models.

        Args:
            batched_inputs (list): a list that contains input to the model.
            proposals (list): a list that contains predicted proposals. Both
                batched_inputs and proposals should have the same length.
        """
        from detectron2.utils.visualizer import Visualizer

        storage = get_event_storage()
        max_vis_prop = 20

        for input, prop in zip(batched_inputs, proposals):
            img = input["image"]
            img = convert_image_to_rgb(img.permute(1, 2, 0), self.input_format)
            v_gt = Visualizer(img, None)
            v_gt = v_gt.overlay_instances(boxes=input["instances"].gt_boxes)
            anno_img = v_gt.get_image()
            box_size = min(len(prop.proposal_boxes), max_vis_prop)
            v_pred = Visualizer(img, None)
            v_pred = v_pred.overlay_instances(
                boxes=prop.proposal_boxes[0:box_size].tensor.cpu().numpy()
            )
            prop_img = v_pred.get_image()
            vis_img = np.concatenate((anno_img, prop_img), axis=1)
            vis_img = vis_img.transpose(2, 0, 1)
            vis_name = "Left: GT bounding boxes;  Right: Predicted proposals"
            storage.put_image(vis_name, vis_img)
            break  # only visualize one image in a batch

    def forward(self, batched_inputs: List[Dict[str, torch.Tensor]]):
        """
        Args:
            batched_inputs: a list, batched outputs of :class:`DatasetMapper` .
                Each item in the list contains the inputs for one image.
                For now, each item in the list is a dict that contains:

                * image: Tensor, image in (C, H, W) format.
                * instances (optional): groundtruth :class:`Instances`
                * proposals (optional): :class:`Instances`, precomputed proposals.

                Other information that's included in the original dicts, such as:

                * "height", "width" (int): the output resolution of the model, used in inference.
                  See :meth:`postprocess` for details.

        Returns:
            list[dict]:
                Each dict is the output for one input image.
                The dict contains one key "instances" whose value is a :class:`Instances`.
                The :class:`Instances` object has the following keys:
                "pred_boxes", "pred_classes", "scores", "pred_masks", "pred_keypoints"
        """
        if not self.training:
            return self.inference(batched_inputs, use_old_features=False)

        images = self.preprocess_image(batched_inputs)
        if "instances" in batched_inputs[0]:
            gt_instances = [x["instances"].to(self.device) for x in batched_inputs]
        else:
            gt_instances = None

        # 使用 Net 处理图像以生成特征图
        features_from_net = self.net(images.tensor)

        # 对 Net 生成的特征图进行归一化和激活函数处理
        normalized_features_from_net = {}
        for key, feature in features_from_net.items():
            normalized_feature = F.normalize(feature, p=2, dim=1)
            activated_feature = F.relu(normalized_feature)
            normalized_features_from_net[key] = activated_feature

        # 使用 backbone 处理图像以生成特征图
        features_from_backbone = self.backbone(images.tensor)

        # 只对p3特征图进行融合
        combined_features = {}
        for key in features_from_backbone.keys():
            if key == 'p2' and key in normalized_features_from_net:
                normalized_features_from_net[key] = F.interpolate(normalized_features_from_net[key], size=(
                features_from_backbone[key].shape[2], features_from_backbone[key].shape[3]), mode='bilinear',
                                                                 align_corners=False)
                combined_feature = 0.3 * normalized_features_from_net[key] + 1 * features_from_backbone[key]
                combined_features[key] = combined_feature
            else:
                combined_features[key] = features_from_backbone[key]

        # 使用 iCaRLNet 处理加权后的特征图
        features_from_icalnet = self.icalnet(combined_features, use_old_features=True)

        # 只对p3特征图进行iCaRLNet融合
        final_features = {}
        for key in combined_features.keys():
            if key == 'p2' and key in features_from_icalnet:
                features_from_icalnet[key] = F.normalize(features_from_icalnet[key], p=2, dim=1)
                features_from_icalnet[key] = F.relu(features_from_icalnet[key])
                final_feature = 1 * combined_features[key] + 0.3 * features_from_icalnet[key]
                final_features[key] = final_feature
            else:
                final_features[key] = combined_features[key]

        if self.proposal_generator is not None:
            proposals, proposal_losses = self.proposal_generator(images, final_features, gt_instances)
        else:
            assert "proposals" in batched_inputs[0]
            proposals = [x["proposals"].to(self.device) for x in batched_inputs]
            proposal_losses = {}

        _, detector_losses = self.roi_heads(images, final_features, proposals, gt_instances)
        if self.vis_period > 0:
            storage = get_event_storage()
            if storage.iter % self.vis_period == 0:
                self.visualize_training(batched_inputs, proposals)

        # 只对p6特征图使用 PINN 处理，生成物理损失
        pinn_losses = {}
        if 'p2' in final_features:
            feature = final_features['p2']
            b, c, h, w = feature.shape
            feature = F.interpolate(feature, size=(160, 160), mode='bilinear', align_corners=False)
            nPt = feature.shape[2] * feature.shape[3]  # 计算特征图中的元素数量
            xStart = 0.
            xEnd = 1.
            yStart = 0.
            yEnd = 0.5

            # 生成 x 和 y 的坐标
            x = np.linspace(xStart, xEnd, feature.shape[2])  # 对应 feature 的宽度
            y = np.linspace(yStart, yEnd, feature.shape[3])  # 对应 feature 的高度
            x, y = np.meshgrid(x, y)
            x = np.reshape(x, (np.size(x[:]), 1))
            y = np.reshape(y, (np.size(y[:]), 1))

            x = torch.tensor(x, dtype=torch.float32, device=self.device)
            y = torch.tensor(y, dtype=torch.float32, device=self.device)

            # 计算物理损失
            loss_pde = self.pinn.criterion(x, y)
            pinn_losses['p2'] = loss_pde

            # 生成边界条件数据
            xb, yb, cb = self.generate_boundary_data(num_points=100)
            loss_bc = self.pinn.Loss_BC(xb, yb, cb)
            pinn_losses['p2_bc'] = loss_bc

        # 计算总损失
        losses = {}
        losses.update(detector_losses)
        losses.update(proposal_losses)
        losses.update(pinn_losses)
        total_loss = sum(losses.values())

        # 记录当前的损失
        self.loss_history.append(total_loss.item())

        # 更新训练步数计数器
        self.step_count += 1

        # 每 100 次迭代进行一次优化
        if self.step_count % 100 == 0:
            self._hybrid_optimization(total_loss.item())

        return losses

    def generate_boundary_data(self, num_points=100):
        # 生成边界点 (x, y) 在边界上
        x = np.random.uniform(0, 1, num_points)
        y = np.zeros(num_points)  # 假设 y=0 是边界
        c = np.sin(np.pi * x)  # 假设边界条件为 sin(pi * x)

        # 将 numpy 数组转换为 torch 张量，并确保形状为 (num_points, 1)
        xb = torch.tensor(x, dtype=torch.float32, device=self.device).unsqueeze(1)
        yb = torch.tensor(y, dtype=torch.float32, device=self.device).unsqueeze(1)
        cb = torch.tensor(c, dtype=torch.float32, device=self.device).unsqueeze(1)

        return xb, yb, cb

    def _hybrid_optimization(self, total_loss):
        if self.optimization_stage == "initial":
            self.a_current = self.simulated_annealing.optimize(self.loss_history)
            if self.step_count >= 1000:  # 假设1000次迭代后进入中间阶段
                self.optimization_stage = "middle"
        elif self.optimization_stage == "middle":
            state = int(total_loss * 10)  # 将损失转换为状态
            action = self.q_learning_agent.select_action(state)
            if action == 0:
                self.a_current -= 0.01  # 减小 a
            elif action == 1:
                self.a_current += 0.01  # 增大 a
            # 更新 Q-learning 模型
            self.q_learning_agent.update(state, action, -total_loss, int(total_loss * 10))
            if self.step_count >= 2000:  # 假设2000次迭代后进入最后阶段
                self.optimization_stage = "final"
        elif self.optimization_stage == "final":
            initial_population = [self.a_current] * self.genetic_algorithm.population_size
            self.a_current = self.genetic_algorithm.optimize(initial_population, self.loss_history)

        # 更新因子 a
        self.icalnet.update_factor_a(self.a_current)

    def inference(
            self,
            batched_inputs: List[Dict[str, torch.Tensor]],
            detected_instances: Optional[List[Instances]] = None,
            do_postprocess: bool = True,
            use_old_features: bool = False,
    ):
        """
        Run inference on the given inputs.

        Args:
            batched_inputs (list[dict]): same as in :meth:`forward`
            detected_instances (None or list[Instances]): if not None, it
                contains an `Instances` object per image. The `Instances`
                object contains "pred_boxes" and "pred_classes" which are
                known boxes in the image.
                The inference will then skip the detection of bounding boxes,
                and only predict other per-ROI outputs.
            do_postprocess (bool): whether to apply post-processing on the outputs.
            use_old_features (bool): whether to use old features for fusion.

        Returns:
            When do_postprocess=True, same as in :meth:`forward`.
            Otherwise, a list[Instances] containing raw network outputs.
        """
        assert not self.training

        images = self.preprocess_image(batched_inputs)
        # 使用 Net 处理图像以生成特征图
        features_from_net = self.net(images.tensor)

        # 对 Net 生成的特征图进行归一化和激活函数处理
        normalized_features_from_net = {}
        for key, feature in features_from_net.items():
            normalized_feature = F.normalize(feature, p=2, dim=1)
            activated_feature = F.relu(normalized_feature)
            normalized_features_from_net[key] = activated_feature

        # 使用 backbone 处理图像以生成特征图
        features_from_backbone = self.backbone(images.tensor)

        # 只对p3特征图进行融合
        combined_features = {}
        for key in features_from_backbone.keys():
            if key == 'p2' and key in normalized_features_from_net:
                normalized_features_from_net[key] = F.interpolate(normalized_features_from_net[key], size=(
                features_from_backbone[key].shape[2], features_from_backbone[key].shape[3]), mode='bilinear',
                                                                 align_corners=False)
                combined_feature = 0.3 * normalized_features_from_net[key] + 1 * features_from_backbone[key]
                combined_features[key] = combined_feature
            else:
                combined_features[key] = features_from_backbone[key]

        # 使用 iCaRLNet 处理加权后的特征图
        features_from_icalnet = self.icalnet(combined_features, use_old_features=use_old_features)

        # 只对p3特征图进行iCaRLNet融合
        final_features = {}
        for key in combined_features.keys():
            if key == 'p2' and key in features_from_icalnet:
                features_from_icalnet[key] = F.normalize(features_from_icalnet[key], p=2, dim=1)
                features_from_icalnet[key] = F.relu(features_from_icalnet[key])
                final_feature = 1 * combined_features[key] + 0.3 * features_from_icalnet[key]
                final_features[key] = final_feature
            else:
                final_features[key] = combined_features[key]

        if detected_instances is None:
            if self.proposal_generator is not None:
                proposals, _ = self.proposal_generator(images, final_features, None)
            else:
                assert "proposals" in batched_inputs[0]
                proposals = [x["proposals"].to(self.device) for x in batched_inputs]

            results, _ = self.roi_heads(images, final_features, proposals, None)
        else:
            detected_instances = [x.to(self.device) for x in detected_instances]
            results = self.roi_heads.forward_with_given_boxes(final_features, detected_instances)

        if do_postprocess:
            assert not torch.jit.is_scripting(), "Scripting is not supported for postprocess."
            return GeneralizedRCNN._postprocess(results, batched_inputs, images.image_sizes)
        return results

    def preprocess_image(self, batched_inputs: List[Dict[str, torch.Tensor]]):
        """
        Normalize, pad and batch the input images.
        """
        images = [self._move_to_current_device(x["image"]) for x in batched_inputs]
        images = [(x - self.pixel_mean) / self.pixel_std for x in images]
        images = ImageList.from_tensors(
            images,
            self.backbone.size_divisibility,
            padding_constraints=self.backbone.padding_constraints,
        )
        return images

    @staticmethod
    def _postprocess(instances, batched_inputs: List[Dict[str, torch.Tensor]], image_sizes):
        """
        Rescale the output instances to the target size.
        """
        # note: private function; subject to changes
        processed_results = []
        for results_per_image, input_per_image, image_size in zip(
                instances, batched_inputs, image_sizes
        ):
            height = input_per_image.get("height", image_size[0])
            width = input_per_image.get("width", image_size[1])
            r = detector_postprocess(results_per_image, height, width)
            processed_results.append({"instances": r})
        return processed_results



@META_ARCH_REGISTRY.register()
class ProposalNetwork(nn.Module):
    """
    A meta architecture that only predicts object proposals.
    """

    @configurable
    def __init__(
            self,
            *,
            backbone: Backbone,
            proposal_generator: nn.Module,
            pixel_mean: Tuple[float],
            pixel_std: Tuple[float],
    ):
        """
        Args:
            backbone: a backbone module, must follow detectron2's backbone interface
            proposal_generator: a module that generates proposals using backbone features
            pixel_mean, pixel_std: list or tuple with #channels element, representing
                the per-channel mean and std to be used to normalize the input image
        """
        super().__init__()
        self.backbone = backbone
        self.proposal_generator = proposal_generator
        self.register_buffer("pixel_mean", torch.tensor(pixel_mean).view(-1, 1, 1), False)
        self.register_buffer("pixel_std", torch.tensor(pixel_std).view(-1, 1, 1), False)

    @classmethod
    def from_config(cls, cfg):
        backbone = build_backbone(cfg)
        return {
            "backbone": backbone,
            "proposal_generator": build_proposal_generator(cfg, backbone.output_shape()),
            "pixel_mean": cfg.MODEL.PIXEL_MEAN,
            "pixel_std": cfg.MODEL.PIXEL_STD,
        }

    @property
    def device(self):
        return self.pixel_mean.device

    def _move_to_current_device(self, x):
        return move_device_like(x, self.pixel_mean)

    def forward(self, batched_inputs):
        """
        Args:
            Same as in :class:`GeneralizedRCNN.forward`

        Returns:
            list[dict]:
                Each dict is the output for one input image.
                The dict contains one key "proposals" whose value is a
                :class:`Instances` with keys "proposal_boxes" and "objectness_logits".
        """
        images = [self._move_to_current_device(x["image"]) for x in batched_inputs]
        images = [(x - self.pixel_mean) / self.pixel_std for x in images]
        images = ImageList.from_tensors(
            images,
            self.backbone.size_divisibility,
            padding_constraints=self.backbone.padding_constraints,
        )
        features = self.backbone(images.tensor)

        if "instances" in batched_inputs[0]:
            gt_instances = [x["instances"].to(self.device) for x in batched_inputs]
        elif "targets" in batched_inputs[0]:
            log_first_n(
                logging.WARN, "'targets' in the model inputs is now renamed to 'instances'!", n=10
            )
            gt_instances = [x["targets"].to(self.device) for x in batched_inputs]
        else:
            gt_instances = None
        proposals, proposal_losses = self.proposal_generator(images, features, gt_instances)
        # In training, the proposals are not useful at all but we generate them anyway.
        # This makes RPN-only models about 5% slower.
        if self.training:
            return proposal_losses

        processed_results = []
        for results_per_image, input_per_image, image_size in zip(
                proposals, batched_inputs, images.image_sizes
        ):
            height = input_per_image.get("height", image_size[0])
            width = input_per_image.get("width", image_size[1])
            r = detector_postprocess(results_per_image, height, width)
            processed_results.append({"proposals": r})
        return processed_results