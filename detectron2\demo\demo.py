# demo.py
# Copyright (c) Facebook, Inc. and its affiliates.
import argparse
import glob
import multiprocessing as mp
import numpy as np
import os
import tempfile
import time
import warnings
import cv2
import tqdm
import matplotlib.pyplot as plt
import csv

from detectron2.config import get_cfg
from detectron2.data.detection_utils import read_image
from detectron2.utils.logger import setup_logger

from predictor import VisualizationDemo

# constants
WINDOW_NAME = "COCO detections"


def setup_cfg(args):
    # load config from file and command-line arguments
    cfg = get_cfg()
    # To use demo for Panoptic-DeepLab, please uncomment the following two lines.
    # from detectron2.projects.panoptic_deeplab import add_panoptic_deeplab_config  # noqa
    # add_panoptic_deeplab_config(cfg)
    cfg.merge_from_file(args.config_file)
    cfg.merge_from_list(args.opts)
    # Set score_threshold for builtin models
    cfg.MODEL.RETINANET.SCORE_THRESH_TEST = args.confidence_threshold
    cfg.MODEL.ROI_HEADS.SCORE_THRESH_TEST = args.confidence_threshold
    cfg.MODEL.PANOPTIC_FPN.COMBINE.INSTANCES_CONFIDENCE_THRESH = args.confidence_threshold
    cfg.freeze()
    return cfg


def get_parser():
    parser = argparse.ArgumentParser(description="Detectron2 demo for builtin configs")
    parser.add_argument(
        "--config-file",
        default="/root/detectron2/configs/COCO-InstanceSegmentation/mask_rcnn_R_50_FPN_3x.yaml",
        metavar="FILE",
        help="path to config file",
    )
    parser.add_argument("--webcam", action="store_true", help="Take inputs from webcam.")
    parser.add_argument("--video-input", help="Path to video file.")
    parser.add_argument(
        "--input",
        nargs="+",
        help="A list of space separated input images; "
             "or a single glob pattern such as 'directory/*.jpg'",
    )
    parser.add_argument(
        "--output",
        help="A file or directory to save output visualizations. "
             "If not given, will show output in an OpenCV window.",
    )

    parser.add_argument(
        "--confidence-threshold",
        type=float,
        default=0.5,
        help="Minimum score for instance predictions to be shown",
    )
    parser.add_argument(
        "--opts",
        help="Modify config options using the command-line 'KEY VALUE' pairs",
        default=[],
        nargs=argparse.REMAINDER,
    )
    return parser


def test_opencv_video_format(codec, file_ext):
    with tempfile.TemporaryDirectory(prefix="video_format_test") as dir:
        filename = os.path.join(dir, "test_file" + file_ext)
        writer = cv2.VideoWriter(
            filename=filename,
            fourcc=cv2.VideoWriter_fourcc(*codec),
            fps=float(30),
            frameSize=(10, 10),
            isColor=True,
        )
        [writer.write(np.zeros((10, 10, 3), np.uint8)) for _ in range(30)]
        writer.release()
        if os.path.isfile(filename):
            return True
        return False


def generate_heatmap(exg_values, masks, img, output_path, instance_ids):
    heatmap = np.zeros_like(img[:, :, 0], dtype=np.float32)  # 创建热图
    for i, mask in enumerate(masks):
        # 将 GPU 上的张量转移到 CPU
        heatmap += mask.cpu().numpy() * exg_values[i]  # 这里修改了
    plt.imshow(heatmap, cmap='hot', interpolation='nearest')
    for i, mask in enumerate(masks):
        y, x = np.where(mask.cpu().numpy())  # 确保 mask 在 CPU 上
        if len(y) > 0 and len(x) > 0:
            plt.text(np.mean(x), np.mean(y), str(instance_ids[i]), color='white', fontsize=12, ha='center', va='center')
    plt.colorbar()
    plt.savefig(output_path)
    plt.close()


def main():
    mp.set_start_method("spawn", force=True)
    args = get_parser().parse_args()
    setup_logger(name="fvcore")
    logger = setup_logger()
    logger.info("Arguments: " + str(args))

    cfg = setup_cfg(args)

    demo = VisualizationDemo(cfg)

    if args.input:
        if len(args.input) == 1:
            args.input = glob.glob(os.path.expanduser(args.input[0]))
            assert args.input, "The input path(s) was not found"
        instance_counter = 1  # 初始化全局实例计数器
        
        # 初始化所有植被指数的值存储
        all_indices_values = {
            'ExG': [], 'VARI': [], 'GRVI': [], 'GLI': [], 'CVI': [], 'CIVE': [], 
            'TGI': [], 'VGI': [], 'ExR': [], 'ExGR': [], 'NDI': [], 'MGRVI': [], 
            'EGRBDI': [], 'RGBRI': [], 'E-NGBDI': [], 'ExB': [], 'IKA_W': []
        }
        
        # 创建主输出目录
        os.makedirs(args.output, exist_ok=True)
        
        # 为每个植被指数创建子目录
        index_dirs = {}
        for index_name in all_indices_values.keys():
            index_dir = os.path.join(args.output, index_name)
            os.makedirs(index_dir, exist_ok=True)
            index_dirs[index_name] = index_dir
        
        # 创建统计文件
        stats_file = os.path.join(args.output, "vegetation_stats.csv")
        
        with open(stats_file, "w", newline='', encoding='utf-8') as csvfile:
            # 创建CSV表头
            header = ["Image", "Instance_ID"]
            for index_name in all_indices_values.keys():
                header.append(index_name)
            header.append("Area (pixels)")
            
            csvwriter = csv.writer(csvfile)
            csvwriter.writerow(header)
            
            for path in tqdm.tqdm(args.input, disable=not args.output):
                img = read_image(path, format="BGR")
                start_time = time.time()
                predictions, visualized_output, indices_outputs, indices_values = demo.run_on_image(img, instance_counter)
                
                if "instances" in predictions:
                    instances = predictions["instances"].to(demo.cpu_device)
                    masks = instances.pred_masks
                    instance_ids = list(range(instance_counter, instance_counter + len(masks)))
                    
                    # 计算每个实例的面积和所有植被指数值
                    for i, (instance_id, mask) in enumerate(zip(instance_ids, masks)):
                        area = mask.sum().item()  # 计算实例的像素面积
                        
                        # 收集该实例的所有指数值
                        row = [os.path.basename(path), instance_id]
                        for index_name in all_indices_values.keys():
                            index_value = indices_values[index_name][i]
                            row.append(index_value)
                            all_indices_values[index_name].append(index_value)
                        row.append(area)
                        
                        # 写入CSV
                        csvwriter.writerow(row)
                    
                    # 更新instance_counter
                    instance_counter += len(masks)
                
                # 保存输出图像
                if args.output:
                    if os.path.isdir(args.output):
                        base_name = os.path.splitext(os.path.basename(path))[0]
                        
                        # 保存实例分割结果
                        out_filename = os.path.join(args.output, f"{base_name}_instances.jpg")
                        cv2.imwrite(out_filename, visualized_output.get_image()[:, :, ::-1])
                        
                        # 保存每个植被指数的可视化结果
                        for index_name, output_image in indices_outputs.items():
                            index_filename = os.path.join(index_dirs[index_name], f"{base_name}_{index_name}.jpg")
                            cv2.imwrite(index_filename, output_image[:, :, ::-1])
        
            # 计算并写入总体统计信息
            csvwriter.writerow([])  # 空行
            csvwriter.writerow(["Overall Statistics"])
            
            # 为每个植被指数计算统计信息
            for index_name, values in all_indices_values.items():
                if values:
                    avg_val = np.mean(values)
                    std_val = np.std(values)
                    min_val = np.min(values)
                    max_val = np.max(values)
                    
                    csvwriter.writerow([])
                    csvwriter.writerow([f"{index_name} Statistics"])
                    csvwriter.writerow(["Average", avg_val])
                    csvwriter.writerow(["Std Dev", std_val])
                    csvwriter.writerow(["Min", min_val])
                    csvwriter.writerow(["Max", max_val])
                    
                    # 打印统计信息到控制台
                    logger.info(f"{index_name} Statistics:")
                    logger.info(f"  Average: {avg_val:.4f}")
                    logger.info(f"  Std Dev: {std_val:.4f}")
                    logger.info(f"  Min: {min_val:.4f}")
                    logger.info(f"  Max: {max_val:.4f}")
    elif args.webcam:
        assert args.input is None, "Cannot have both --input and --webcam!"
        assert args.output is None, "output not yet supported with --webcam!"
        cam = cv2.VideoCapture(0)
        instance_counter = 1  # 初始化实例计数器
        with open(os.path.join(args.output, "tree_exg.csv"), "w", newline='') as csvfile:
            csvwriter = csv.writer(csvfile)
            csvwriter.writerow(["Instance ID", "ExG"])
            for vis in tqdm.tqdm(demo.run_on_video(cam)):
                cv2.namedWindow(WINDOW_NAME, cv2.WINDOW_NORMAL)
                cv2.imshow(WINDOW_NAME, vis)
                if cv2.waitKey(1) == 27:
                    break  # esc to quit
        cam.release()
        cv2.destroyAllWindows()
    elif args.video_input:
        video = cv2.VideoCapture(args.video_input)
        width = int(video.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(video.get(cv2.CAP_PROP_FRAME_HEIGHT))
        frames_per_second = video.get(cv2.CAP_PROP_FPS)
        num_frames = int(video.get(cv2.CAP_PROP_FRAME_COUNT))
        basename = os.path.basename(args.video_input)
        codec, file_ext = (
            ("x264", ".mkv") if test_opencv_video_format("x264", ".mkv") else ("mp4v", ".mp4")
        )
        if codec == ".mp4v":
            warnings.warn("x264 codec not available, switching to mp4v")
        if args.output:
            if os.path.isdir(args.output):
                output_fname = os.path.join(args.output, basename)
                output_fname = os.path.splitext(output_fname)[0] + file_ext
            else:
                output_fname = args.output
            assert not os.path.isfile(output_fname), output_fname
            output_file = cv2.VideoWriter(
                filename=output_fname,
                # some installation of opencv may not support x264 (due to its license),
                # you can try other format (e.g. MPEG)
                fourcc=cv2.VideoWriter_fourcc(*codec),
                fps=float(frames_per_second),
                frameSize=(width, height),
                isColor=True,
            )
        assert os.path.isfile(args.video_input)
        instance_counter = 1  # 初始化实例计数器
        with open(os.path.join(args.output, "tree_exg.csv"), "w", newline='') as csvfile:
            csvwriter = csv.writer(csvfile)
            csvwriter.writerow(["Instance ID", "ExG"])
            for vis_frame in tqdm.tqdm(demo.run_on_video(video), total=num_frames):
                if args.output:
                    output_file.write(vis_frame)
                else:
                    cv2.namedWindow(basename, cv2.WINDOW_NORMAL)
                    cv2.imshow(basename, vis_frame)
                    if cv2.waitKey(1) == 27:
                        break  # esc to quit
        video.release()
        if args.output:
            output_file.release()
        else:
            cv2.destroyAllWindows()


if __name__ == "__main__":
    main()
