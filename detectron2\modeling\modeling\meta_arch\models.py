# models.py
import torch
import torch.nn as nn
import torch.nn.functional as F
from .mamba import Mamba, ModelArgs
import torch.nn.init as init
from einops import rearrange, repeat




class AdaptiveGaussianFilter(nn.Module):
    def __init__(self, in_channels, kernel_size=5, min_sigma=0.5, max_sigma=2.0):
        super(AdaptiveGaussianFilter, self).__init__()
        self.kernel_size = kernel_size
        self.min_sigma = min_sigma
        self.max_sigma = max_sigma
        self.in_channels = in_channels

    def _create_gaussian_kernel(self, sigma):
        # 创建一个标准的高斯核
        x = torch.arange(-self.kernel_size // 2 + 1., self.kernel_size // 2 + 1.)
        y = torch.arange(-self.kernel_size // 2 + 1., self.kernel_size // 2 + 1.)
        xx, yy = torch.meshgrid(x, y, indexing='ij')
        kernel = torch.exp(-(xx ** 2 + yy ** 2) / (2. * sigma ** 2))
        kernel /= kernel.sum()
        # 这里不使用repeat，直接创建每个通道一个滤波器核
        kernel = kernel.unsqueeze(0).unsqueeze(0)
        # 把核心扩展到所需的形状 [in_channels, 1, kernel_size, kernel_size]
        return kernel

    def _compute_local_variance(self, x):
        # 计算局部区域方差
        padding = self.kernel_size // 2
        mean = F.avg_pool2d(x, self.kernel_size, stride=1, padding=padding)
        squared_mean = F.avg_pool2d(x ** 2, self.kernel_size, stride=1, padding=padding)
        variance = squared_mean - mean ** 2
        return variance

    def forward(self, x):
        # 计算局部区域的方差
        variance = self._compute_local_variance(x)

        # 根据方差动态计算标准差
        sigma = torch.sqrt(torch.clamp(variance, min=0) + 1e-6)  # 方差需要大于0
        sigma = torch.clamp(sigma, min=self.min_sigma, max=self.max_sigma)  # 限制sigma的范围

        # 使用动态的sigma生成高斯核
        avg_sigma = sigma.mean().item()  # 使用平均sigma简化计算
        base_kernel = self._create_gaussian_kernel(avg_sigma)
        
        # 确保输入通道数匹配
        # 将基础核扩展到所需的[in_channels, 1, kernel_size, kernel_size]形状
        kernels = base_kernel.repeat(x.shape[1], 1, 1, 1)

        # 使用自适应的高斯核进行卷积
        output = F.conv2d(x, kernels.to(x.device), padding=self.kernel_size // 2, groups=x.shape[1])
        return output


class ComplexConv2d(nn.Module):
    def __init__(self, in_channels, out_channels, kernel_size, stride=1, padding=0, device='cpu'):
        super(ComplexConv2d, self).__init__()
        self.device = device
        self.real_conv = nn.Conv2d(in_channels, out_channels, kernel_size, stride, padding).to(self.device)
        self.imag_conv = nn.Conv2d(in_channels, out_channels, kernel_size, stride, padding).to(self.device)

    def forward(self, x):
        if x.dtype != torch.complex64:
            x = torch.complex(x, torch.zeros_like(x).to(self.device))  # 转为复数张量
        real_part = self.real_conv(torch.real(x))  # 实部卷积
        imag_part = self.imag_conv(torch.imag(x))  # 虚部卷积
        return torch.complex(real_part, imag_part)



class LaplacianTransform(nn.Module):
    def __init__(self, scales, use_adaptive_filtering=True):
        super(LaplacianTransform, self).__init__()
        self.scales = scales
        self.use_adaptive_filtering = use_adaptive_filtering

    def forward(self, x):
        # 获取输入张量的设备
        device = x.device
        
        # 确保输入是4D张量 (batch_size, channels, height, width)
        if x.dim() == 3:  # 如果输入是3D，增加一个 batch_size 维度
            x = x.unsqueeze(0)
            
        # 存储输入通道数，供_create_laplacian_kernel使用
        self._input_channels = x.shape[1]

        if x.dtype == torch.complex64:
            # 提取实部和虚部
            real_part = torch.real(x)
            imag_part = torch.imag(x)
        else:
            # 如果是实数张量，将其转为复数张量的输入形式
            real_part = x
            imag_part = torch.zeros_like(x)

        # 对实部和虚部分别应用不同尺度的拉普拉斯算子
        laplacian_kernels = []
        for scale in self.scales:
            # 获取基础卷积核
            base_kernel = self._create_laplacian_kernel(scale, device)
            # 在这里动态复制到所需通道数
            full_kernel = base_kernel.repeat(x.shape[1], 1, 1, 1)
            laplacian_kernels.append(full_kernel)

        # 使用自适应的高斯滤波器
        if self.use_adaptive_filtering:
            adaptive_filter = AdaptiveGaussianFilter(in_channels=x.shape[1])
            # 确保滤波器在同一设备上
            adaptive_filter = adaptive_filter.to(device)

        # 临时存储不同尺度的特征
        scale_features = []

        for i, laplacian_kernel in enumerate(laplacian_kernels):
            # 获取当前尺度
            scale = self.scales[i]
            
            # 对实部和虚部分别应用拉普拉斯算子
            real_output = F.conv2d(real_part, laplacian_kernel, groups=real_part.size(1), padding=scale // 2)
            imag_output = F.conv2d(imag_part, laplacian_kernel, groups=imag_part.size(1), padding=scale // 2)

            # 合并为通道维度扩展的张量
            combined_features = torch.cat([real_output, imag_output], dim=1)

            # 应用自适应的高斯滤波器
            if self.use_adaptive_filtering:
                combined_features = adaptive_filter(combined_features)
                
            # 保存处理后的特征
            scale_features.append(combined_features)
        
        # 确保所有特征具有相同的形状
        # 以输入x的形状为目标形状
        target_shape = x.shape
        
        # 融合所有尺度的特征
        # 首先调整所有特征到相同大小
        for i in range(len(scale_features)):
            if scale_features[i].shape[2:] != target_shape[2:]:
                scale_features[i] = F.interpolate(scale_features[i], size=target_shape[2:], mode='bilinear', align_corners=False)
        
        # 使用不同的策略融合特征
        # 方法1：简单平均所有尺度的特征
        all_features = torch.stack(scale_features, dim=0)
        avg_features = torch.mean(all_features, dim=0)
        
        # 将通道数调整为与输入相同
        # 创建一个1x1卷积权重，用于将通道数从avg_features.shape[1]变为x.shape[1]
        channel_reducer = torch.ones(x.shape[1], avg_features.shape[1] // x.shape[1], 1, 1).to(device)
        output = F.conv2d(avg_features, channel_reducer, groups=x.shape[1])

        return output

    def _create_laplacian_kernel(self, scale, device):
        # 定义拉普拉斯算子卷积核 - 多尺度设计
        if scale == 3:
            laplacian_kernel = torch.tensor([
                [1, 2, 1],
                [2, -12, 2],
                [1, 2, 1]
            ], dtype=torch.float32).unsqueeze(0).unsqueeze(0)
        elif scale == 5:
            laplacian_kernel = torch.tensor([
                [-2, -4, -4, -4, -2],
                [-4, 0, 8, 0, -4],
                [-4, 8, 24, 8, -4],
                [-4, 0, 8, 0, -4],
                [-2, -4, -4, -4, -2]
            ], dtype=torch.float32).unsqueeze(0).unsqueeze(0)
        elif scale == 7:
            laplacian_kernel = torch.tensor([
                [-1, -1, -1, -2, -1, -1, -1],
                [-1, -2, -4, -8, -4, -2, -1],
                [-1, -4, 0, 16, 0, -4, -1],
                [-2, -8, 16, 36, 16, -8, -2],
                [-1, -4, 0, 16, 0, -4, -1],
                [-1, -2, -4, -8, -4, -2, -1],
                [-1, -1, -1, -2, -1, -1, -1]
            ], dtype=torch.float32).unsqueeze(0).unsqueeze(0)
        else:
            raise ValueError("Unsupported scale")
            
        # 确保卷积核在同一设备上
        return laplacian_kernel.to(device)

    def _create_fusion_kernel(self, index, device):
        # 创建1x1卷积核用于融合不同尺度的特征
        # 获取输入通道数
        in_channels = getattr(self, '_input_channels', 1) * 2  # 因为合并了real和imag，所以是*2
        
        # 为每个输出通道创建对应的输入过滤器
        # 正确的卷积核形状：[输出通道数, 输入通道数, 高度, 宽度]
        # 此处输出通道数为1，输入通道数为in_channels
        kernel = torch.zeros(1, in_channels, 1, 1, dtype=torch.float32)
        
        # 根据索引选择特定尺度的特征
        # 在这个简化的设计中，我们将对应索引的权重设为1.0，其他为0
        # 这样可以从不同尺度中选择特定的特征
        kernel[0, index*2:(index+1)*2, 0, 0] = 1.0
        
        # 确保卷积核在同一设备上
        return kernel.to(device)



class ResidualBlock(nn.Module):
    def __init__(self, in_channels, out_channels, stride=1, use_se=True):
        super(ResidualBlock, self).__init__()
        self.conv1 = nn.Conv2d(in_channels, out_channels, kernel_size=3, stride=stride, padding=1, bias=False)
        self.bn1 = nn.BatchNorm2d(out_channels)
        self.relu = nn.ReLU(inplace=True)
        self.conv2 = nn.Conv2d(out_channels, out_channels, kernel_size=3, stride=1, padding=1, bias=False)
        self.bn2 = nn.BatchNorm2d(out_channels)
        
        # SE注意力块，提高特征表达能力
        if use_se:
            self.se = SEBlock(out_channels)

        self.shortcut = nn.Sequential()
        if stride != 1 or in_channels != out_channels:
            self.shortcut = nn.Sequential(
                nn.Conv2d(in_channels, out_channels, kernel_size=1, stride=stride, bias=False),
                nn.BatchNorm2d(out_channels)
            )

    def forward(self, x):
        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)
        out = self.conv2(out)
        out = self.bn2(out)
        
        # 应用SE注意力
        if hasattr(self, 'se'):
            out = self.se(out)
        
        out += self.shortcut(x)
        out = self.relu(out)
        return out


class SEBlock(nn.Module):
    """通道注意力机制"""
    def __init__(self, channel, reduction=16):
        super(SEBlock, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.fc = nn.Sequential(
            nn.Linear(channel, channel // reduction, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(channel // reduction, channel, bias=False),
            nn.Sigmoid()
        )

    def forward(self, x):
        b, c, _, _ = x.size()
        y = self.avg_pool(x).view(b, c)
        y = self.fc(y).view(b, c, 1, 1)
        return x * y.expand_as(x)


class MambaFeatureProcessor(nn.Module):
    """Mamba特征处理模块"""
    def __init__(self, d_model, d_state, d_conv, expand):
        super(MambaFeatureProcessor, self).__init__()
        # 更深的Mamba配置
        mamba_args = ModelArgs(
            d_model=d_model, 
            n_layer=3, 
            d_state=d_state,
            dt_rank=d_conv,
            expand=expand
        )
        self.mamba = Mamba(mamba_args)
        
        # 特征增强层
        self.feature_enhance = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Linear(d_model, d_model),
            nn.GELU()
        )
        
        # 投影层
        self.proj = nn.Linear(d_model, d_model)

    def forward(self, x):
        # 输入 x 形状: [B, C, H, W]
        b, c, h, w = x.shape
        
        # 重排为序列形式，同时保留空间位置信息
        x_flat = rearrange(x, 'b c h w -> b (h w) c')
        
        # 通过 Mamba 处理序列数据
        mamba_out = self.mamba(x_flat)
        
        # 特征增强
        enhanced = self.feature_enhance(mamba_out)
        
        # 残差连接
        output = self.proj(enhanced + x_flat)
        
        # 重排回空间形式
        output = rearrange(output, 'b (h w) c -> b c h w', h=h, w=w)
        
        return output


class Net(nn.Module):
    def __init__(self, in_features=3, out_features=256, num_channels=64):
        """
        增强版的主干特征提取网络，整合了拉普拉斯变换、傅里叶变换、残差块和Mamba特征处理器
        
        Args:
            in_features (int): 输入通道数
            out_features (int): 输出通道数
            num_channels (int): 中间层通道数
        """
        super(Net, self).__init__()
        
        # 拉普拉斯变换处理器
        self.laplacian = LaplacianTransform(scales=[3, 5, 7], use_adaptive_filtering=True)
        
        # 傅里叶变换处理
        self.use_fourier = True
        
        # 计算实际输入通道数
        # 原始输入通道数
        self.lap_channels = in_features  # 拉普拉斯特征与输入通道数相同
        self.fourier_channels = 2 if self.use_fourier else 0  # 傅里叶特征通道数
        
        # 初始特征提取
        input_channels = in_features + self.lap_channels + self.fourier_channels
            
        self.initial_conv = nn.Sequential(
            nn.Conv2d(input_channels, num_channels, kernel_size=7, stride=2, padding=3, bias=False),
            nn.BatchNorm2d(num_channels),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=3, stride=2, padding=1)
        )
        
        # 残差块构建四个特征层级
        self.layer1 = nn.Sequential(
            ResidualBlock(num_channels, num_channels, use_se=True),
            ResidualBlock(num_channels, num_channels, use_se=True)
        )
        
        self.layer2 = nn.Sequential(
            ResidualBlock(num_channels, num_channels * 2, stride=2, use_se=True),
            ResidualBlock(num_channels * 2, num_channels * 2, use_se=True)
        )
        
        self.layer3 = nn.Sequential(
            ResidualBlock(num_channels * 2, num_channels * 4, stride=2, use_se=True),
            ResidualBlock(num_channels * 4, num_channels * 4, use_se=True)
        )
        
        self.layer4 = nn.Sequential(
            ResidualBlock(num_channels * 4, num_channels * 8, stride=2, use_se=True),
            ResidualBlock(num_channels * 8, num_channels * 8, use_se=True)
        )
        
        # Mamba特征处理器，用于各个特征层级
        # 减小参数以降低内存占用
        self.mamba_p2 = MambaFeatureProcessor(
            d_model=num_channels,
            d_state=16,   # 从32减小到16
            d_conv=2,     # 从4减小到2
            expand=2      # 从4减小到2
        )
        
        self.mamba_p3 = MambaFeatureProcessor(
            d_model=num_channels * 2,
            d_state=16,
            d_conv=2,
            expand=2
        )
        
        self.mamba_p4 = MambaFeatureProcessor(
            d_model=num_channels * 4,
            d_state=16,
            d_conv=2,
            expand=2
        )
        
        self.mamba_p5 = MambaFeatureProcessor(
            d_model=num_channels * 8,
            d_state=16,
            d_conv=2,
            expand=2
        )
        
        # 横向连接，用于特征融合
        self.lateral_p5 = nn.Conv2d(num_channels * 8, out_features, kernel_size=1)
        self.lateral_p4 = nn.Conv2d(num_channels * 4, out_features, kernel_size=1)
        self.lateral_p3 = nn.Conv2d(num_channels * 2, out_features, kernel_size=1)
        self.lateral_p2 = nn.Conv2d(num_channels, out_features, kernel_size=1)
        
        # FPN自顶向下路径
        self.fpn_p4 = nn.Conv2d(out_features, out_features, kernel_size=3, padding=1)
        self.fpn_p3 = nn.Conv2d(out_features, out_features, kernel_size=3, padding=1)
        self.fpn_p2 = nn.Conv2d(out_features, out_features, kernel_size=3, padding=1)
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, std=0.01)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        """
        前向传播过程
        
        Args:
            x: 输入张量，形状为 [B, C, H, W]
            
        Returns:
            dict: 包含多尺度特征图的字典
        """
        # 应用拉普拉斯变换获取边缘增强特征
        lap_x = self.laplacian(x)
        
        # 添加傅里叶变换处理
        fourier_features = []
        if self.use_fourier:
            # 计算傅里叶变换
            fft_result = torch.fft.fft2(x)
            # 获取频谱的幅度和相位
            real_part = torch.real(fft_result)
            imag_part = torch.imag(fft_result)
            # 将频谱移到中心
            real_part = torch.fft.fftshift(real_part)
            imag_part = torch.fft.fftshift(imag_part)
            # 取对数增强对比度（处理幅度）
            fourier_features = [
                torch.log(torch.abs(real_part) + 1.0),
                torch.log(torch.abs(imag_part) + 1.0)
            ]
        
        # 拼接原始输入、拉普拉斯特征和傅里叶特征
        features_to_concat = [x, lap_x]
        if self.use_fourier:
            features_to_concat.extend(fourier_features)
        
        x = torch.cat(features_to_concat, dim=1)
        
        # 动态调整初始卷积层的权重以匹配输入通道数
        if self.initial_conv[0].weight.shape[1] != x.shape[1]:
            # 原始卷积层权重
            old_weight = self.initial_conv[0].weight
            old_channels = old_weight.shape[1]
            new_channels = x.shape[1]
            out_channels = old_weight.shape[0]
            k_size = old_weight.shape[2]
            
            # 创建新的卷积层
            new_conv = nn.Conv2d(
                new_channels, 
                out_channels,
                kernel_size=k_size,
                stride=self.initial_conv[0].stride,
                padding=self.initial_conv[0].padding,
                bias=False
            ).to(x.device)
            
            # 对新权重进行初始化
            nn.init.kaiming_normal_(new_conv.weight, mode='fan_out', nonlinearity='relu')
            
            # 复制旧权重到新权重（尽可能多地保留信息）
            with torch.no_grad():
                # 如果新通道数大于旧通道数，复制原有权重并初始化新的部分
                if new_channels > old_channels:
                    new_conv.weight[:, :old_channels, :, :] = old_weight
                # 如果新通道数小于旧通道数，只复制对应部分的权重
                else:
                    new_conv.weight = nn.Parameter(old_weight[:, :new_channels, :, :])
            
            # 替换原有卷积层
            self.initial_conv[0] = new_conv
        
        # 初始特征提取
        c1 = self.initial_conv(x)
        
        # 构建多尺度特征金字塔
        c2 = self.layer1(c1)                 # 1/4 分辨率
        c3 = self.layer2(c2)                 # 1/8 分辨率
        c4 = self.layer3(c3)                 # 1/16 分辨率
        c5 = self.layer4(c4)                 # 1/32 分辨率
        
        # 应用Mamba特征处理器
        c2_mamba = self.mamba_p2(c2)
        c3_mamba = self.mamba_p3(c3)
        c4_mamba = self.mamba_p4(c4)
        c5_mamba = self.mamba_p5(c5)
        
        # 横向连接 - 使用残差连接方式融合原始特征和mamba处理后的特征
        p5 = self.lateral_p5(c5_mamba + c5 * 0.5)  # 添加原始特征的残差连接
        p4 = self.lateral_p4(c4_mamba + c4 * 0.5) + F.interpolate(p5, size=c4.shape[2:], mode='nearest')
        p3 = self.lateral_p3(c3_mamba + c3 * 0.5) + F.interpolate(p4, size=c3.shape[2:], mode='nearest')
        p2 = self.lateral_p2(c2_mamba + c2 * 0.5) + F.interpolate(p3, size=c2.shape[2:], mode='nearest')
        
        # FPN自顶向下处理
        p4 = self.fpn_p4(p4)
        p3 = self.fpn_p3(p3)
        p2 = self.fpn_p2(p2)
        
        # 返回多尺度特征图
        return {
            "p2": p2,  # 1/4 分辨率
            "p3": p3,  # 1/8 分辨率
            "p4": p4,  # 1/16 分辨率
            "p5": p5,  # 1/32 分辨率
        }
    
    def train(self, mode=True):
        """
        设置模型训练模式
        覆盖父类方法，确保BN层正确行为
        """
        super(Net, self).train(mode)
        
        # 冻结所有BN层统计量，提高小批量训练稳定性
        if mode:
            for m in self.modules():
                if isinstance(m, nn.BatchNorm2d):
                    m.eval()
                    m.weight.requires_grad = False
                    m.bias.requires_grad = False
        
        return self


