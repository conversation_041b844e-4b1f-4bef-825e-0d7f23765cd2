# output dir
output
instant_test_output
inference_test_output


*.png
*.json
*.diff
*.jpg
!/projects/DensePose/doc/images/*.jpg

# compilation and distribution
__pycache__
_ext
*.pyc
*.pyd
*.so
*.dll
*.egg-info/
build/
dist/
wheels/

# pytorch/python/numpy formats
*.pth
*.pkl
*.npy
*.ts
model_ts*.txt

# ipython/jupyter notebooks
*.ipynb
**/.ipynb_checkpoints/

# Editor temporaries
*.swn
*.swo
*.swp
*~

# editor settings
.idea
.vscode
_darcs

# project dirs
/detectron2/model_zoo/configs
/datasets/*
!/datasets/*.*
/projects/*/datasets
/models
/snippet
