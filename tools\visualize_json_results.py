# Copyright (c) Facebook, Inc. and its affiliates.
import logging
import numpy as np
from typing import Dict, List, Optional, Tuple
import torch
from torch import nn

from detectron2.config import configurable
from detectron2.data.detection_utils import convert_image_to_rgb
from detectron2.layers import move_device_like
from detectron2.structures import ImageList, Instances
from detectron2.utils.events import get_event_storage
from detectron2.utils.logger import log_first_n
import torch.nn.functional as F
from ..backbone import Backbone, build_backbone
from .models import Net
from .CL import iCaRLNet
from .pinn import PINN  # 导入 PINN 模块

from ..postprocessing import detector_postprocess
from ..proposal_generator import build_proposal_generator
from ..roi_heads import build_roi_heads
from .build import META_ARCH_REGISTRY

__all__ = ["GeneralizedRCNN", "ProposalNetwork"]


@META_ARCH_REGISTRY.register()
class GeneralizedRCNN(nn.Module):
    """
    Generalized R-CNN with integrated Mamba, PINN, and iCaRLNet modules.
    """

    @configurable
    def __init__(
        self,
        *,
        backbone: Backbone,
        proposal_generator: nn.Module,
        roi_heads: nn.Module,
        net: nn.Module,
        icalnet: nn.Module,
        pinn: nn.Module,  # 添加 PINN 模块
        pixel_mean: Tuple[float],
        pixel_std: Tuple[float],
        input_format: Optional[str] = None,
        vis_period: int = 0,
        alpha: float = 0.4,  # iCaRLNet 特征权重
        beta: float = 0.4,  # PINN 特征权重
        gamma: float = 0.2,  # 主特征权重
    ):
        """
        Args:
            backbone: a backbone module, must follow detectron2's backbone interface
            proposal_generator: a module that generates proposals using backbone features
            roi_heads: a ROI head that performs per-region computation
            net: a module that processes images to generate feature maps
            icalnet: a module that processes feature maps
            pinn: a module that processes feature maps using PINN
            pixel_mean, pixel_std: list or tuple with #channels element, representing
                the per-channel mean and std to be used to normalize the input image
            input_format: describe the meaning of channels of input. Needed by visualization
            vis_period: the period to run visualization. Set to 0 to disable.
            alpha: weight for iCaRLNet features
            beta: weight for PINN features
            gamma: weight for main features
        """
        super().__init__()
        self.backbone = backbone
        self.proposal_generator = proposal_generator
        self.roi_heads = roi_heads
        self.net = net
        self.icalnet = icalnet
        self.pinn = pinn

        self.input_format = input_format
        self.vis_period = vis_period
        if vis_period > 0:
            assert input_format is not None, "input_format is required for visualization!"

        self.register_buffer("pixel_mean", torch.tensor(pixel_mean).view(-1, 1, 1), False)
        self.register_buffer("pixel_std", torch.tensor(pixel_std).view(-1, 1, 1), False)
        assert (
            self.pixel_mean.shape == self.pixel_std.shape
        ), f"{self.pixel_mean} and {self.pixel_std} have different shapes!"

        # 添加权重参数
        self.alpha = alpha
        self.beta = beta
        self.gamma = gamma

        # 定义优化器
        self.optimizer = torch.optim.Adam(self.parameters(), lr=0.001)

        # 定义 PINN 的损失权重
        self.lambda_pde = 1.0
        self.lambda_bc = 1.0

    @classmethod
    def from_config(cls, cfg):
        backbone = build_backbone(cfg)
        return {
            "backbone": backbone,
            "proposal_generator": build_proposal_generator(cfg, backbone.output_shape()),
            "roi_heads": build_roi_heads(cfg, backbone.output_shape()),
            "net": Net(),  # 添加 Net 模块
            "icalnet": iCaRLNet(feature_sizes={'p2': 256, 'p3': 256, 'p4': 256, 'p5': 256, 'p6': 256}),  # 添加 iCaRLNet 模块
            "pinn": PINN(),  # 添加 PINN 模块
            "input_format": cfg.INPUT.FORMAT,
            "vis_period": cfg.VIS_PERIOD,
            "pixel_mean": cfg.MODEL.PIXEL_MEAN,
            "pixel_std": cfg.MODEL.PIXEL_STD,
            "alpha": 0.4,  # iCaRLNet 特征权重
            "beta": 0.4,  # PINN 特征权重
            "gamma": 0.2,  # 主特征权重
        }

    @property
    def device(self):
        return self.pixel_mean.device

    def _move_to_current_device(self, x):
        return move_device_like(x, self.pixel_mean)

    def visualize_training(self, batched_inputs, proposals):
        """
        A function used to visualize images and proposals. It shows ground truth
        bounding boxes on the original image and up to 20 top-scoring predicted
        object proposals on the original image. Users can implement different
        visualization functions for different models.

        Args:
            batched_inputs (list): a list that contains input to the model.
            proposals (list): a list that contains predicted proposals. Both
                batched_inputs and proposals should have the same length.
        """
        from detectron2.utils.visualizer import Visualizer

        storage = get_event_storage()
        max_vis_prop = 20

        for input, prop in zip(batched_inputs, proposals):
            img = input["image"]
            img = convert_image_to_rgb(img.permute(1, 2, 0), self.input_format)
            v_gt = Visualizer(img, None)
            v_gt = v_gt.overlay_instances(boxes=input["instances"].gt_boxes)
            anno_img = v_gt.get_image()
            box_size = min(len(prop.proposal_boxes), max_vis_prop)
            v_pred = Visualizer(img, None)
            v_pred = v_pred.overlay_instances(
                boxes=prop.proposal_boxes[0:box_size].tensor.cpu().numpy()
            )
            prop_img = v_pred.get_image()
            vis_img = np.concatenate((anno_img, prop_img), axis=1)
            vis_img = vis_img.transpose(2, 0, 1)
            vis_name = "Left: GT bounding boxes;  Right: Predicted proposals"
            storage.put_image(vis_name, vis_img)
            break  # only visualize one image in a batch

    def forward(self, batched_inputs: List[Dict[str, torch.Tensor]]):
        """
        Args:
            batched_inputs: a list, batched outputs of :class:`DatasetMapper` .
                Each item in the list contains the inputs for one image.
                For now, each item in the list is a dict that contains:

                * image: Tensor, image in (C, H, W) format.
                * instances (optional): groundtruth :class:`Instances`
                * proposals (optional): :class:`Instances`, precomputed proposals.

                Other information that's included in the original dicts, such as:

                * "height", "width" (int): the output resolution of the model, used in inference.
                  See :meth:`postprocess` for details.

        Returns:
            list[dict]:
                Each dict is the output for one input image.
                The dict contains one key "instances" whose value is a :class:`Instances`.
                The :class:`Instances` object has the following keys:
                "pred_boxes", "pred_classes", "scores", "pred_masks", "pred_keypoints"
        """
        if not self.training:
            return self.inference(batched_inputs)

        images = self.preprocess_image(batched_inputs)
        if "instances" in batched_inputs[0]:
            gt_instances = [x["instances"].to(self.device) for x in batched_inputs]
        else:
            gt_instances = None

        # 使用 Net 处理图像以生成特征图
        features_from_net = self.net(images.tensor)

        # 对 Net 生成的特征图进行归一化和激活函数处理
        normalized_features_from_net = {}
        for key, feature in features_from_net.items():
            normalized_feature = F.normalize(feature, p=2, dim=1)
            activated_feature = F.relu(normalized_feature)
            normalized_features_from_net[key] = activated_feature

        # 使用 backbone 处理图像以生成特征图
        features_from_backbone = self.backbone(images.tensor)

        # 将 Net 和 backbone 生成的特征图按权重加权连接
        combined_features = {}
        for key in features_from_backbone.keys():
            if key in normalized_features_from_net:
                normalized_features_from_net[key] = F.interpolate(normalized_features_from_net[key], size=(features_from_backbone[key].shape[2], features_from_backbone[key].shape[3]), mode='bilinear',
                                               align_corners=False)
                combined_feature = 0.01 * normalized_features_from_net[key] + 1 * features_from_backbone[key]
                combined_features[key] = combined_feature

        # 使用 iCaRLNet 处理加权后的特征图
        features_from_icalnet = self.icalnet(combined_features)

        # 将 iCaRLNet 处理后的特征图与原始加权特征图按权重加权连接
        final_features = {}
        for key in combined_features.keys():
            if key in features_from_icalnet:
                features_from_icalnet[key] = F.normalize(features_from_icalnet[key], p=2, dim=1)
                features_from_icalnet[key] = F.relu(features_from_icalnet[key])
                final_feature = 1 * combined_features[key] + 0.01 * features_from_icalnet[key]
                final_features[key] = final_feature

        # 使用 PINN 处理特征图以生成物理损失
        pinn_losses = {}
        for key in final_features.keys():
            if key == 'p2':  # 假设只对 p2 特征图应用 PINN
                feature = final_features[key]
                b, c, h, w = feature.shape
                feature = F.interpolate(feature, size=(160, 160), mode='bilinear', align_corners=False)
                nPt = feature.shape[2] * feature.shape[3]  # 计算特征图中的元素数量
                xStart = 0.
                xEnd = 1.
                yStart = 0.
                yEnd = 0.5

                # 生成 x 和 y 的坐标
                x = np.linspace(xStart, xEnd, feature.shape[2])  # 对应 feature 的宽度
                y = np.linspace(yStart, yEnd, feature.shape[3])  # 对应 feature 的高度
                x, y = np.meshgrid(x, y)
                x = np.reshape(x, (np.size(x[:]), 1))
                y = np.reshape(y, (np.size(y[:]), 1))

                x = torch.tensor(x, dtype=torch.float32, device=self.device)
                y = torch.tensor(y, dtype=torch.float32, device=self.device)

                # 计算物理损失
                loss_pde = self.pinn.criterion(x, y)
                pinn_losses[key] = loss_pde

                # 生成边界条件数据
                xb, yb, cb = self.generate_boundary_data(num_points=100)
                loss_bc = self.pinn.Loss_BC(xb, yb, cb)
                pinn_losses[f'{key}_bc'] = loss_bc

        if self.proposal_generator is not None:
            proposals, proposal_losses = self.proposal_generator(images, final_features, gt_instances)
        else:
            assert "proposals" in batched_inputs[0]
            proposals = [x["proposals"].to(self.device) for x in batched_inputs]
            proposal_losses = {}

        _, detector_losses = self.roi_heads(images, final_features, proposals, gt_instances)
        if self.vis_period > 0:
            storage = get_event_storage()
            if storage.iter % self.vis_period == 0:
                self.visualize_training(batched_inputs, proposals)

        losses = {}
        losses.update(detector_losses)
        losses.update(proposal_losses)
        losses.update(pinn_losses)

        return losses

    def inference(
        self,
        batched_inputs: List[Dict[str, torch.Tensor]],
        detected_instances: Optional[List[Instances]] = None,
        do_postprocess: bool = True,
    ):
        """
        Run inference on the given inputs.

        Args:
            batched_inputs (list[dict]): same as in :meth:`forward`
            detected_instances (None or list[Instances]): if not None, it
                contains an `Instances` object per image. The `Instances`
                object contains "pred_boxes" and "pred_classes" which are
                known boxes in the image.
                The inference will then skip the detection of bounding boxes,
                and only predict other per-ROI outputs.
            do_postprocess (bool): whether to apply post-processing on the outputs.

        Returns:
            When do_postprocess=True, same as in :meth:`forward`.
            Otherwise, a list[Instances] containing raw network outputs.
        """
        assert not self.training

        images = self.preprocess_image(batched_inputs)
        # 使用 Net 处理图像以生成特征图
        features_from_net = self.net(images.tensor)

        # 对 Net 生成的特征图进行归一化和激活函数处理
        normalized_features_from_net = {}
        for key, feature in features_from_net.items():
            normalized_feature = F.normalize(feature, p=2, dim=1)
            activated_feature = F.relu(normalized_feature)
            normalized_features_from_net[key] = activated_feature

        # 使用 backbone 处理图像以生成特征图
        features_from_backbone = self.backbone(images.tensor)

        # 将 Net 和 backbone 生成的特征图按权重加权连接
        combined_features = {}
        for key in features_from_backbone.keys():
            if key in normalized_features_from_net:
                normalized_features_from_net[key] = F.interpolate(normalized_features_from_net[key], size=(features_from_backbone[key].shape[2], features_from_backbone[key].shape[3]), mode='bilinear',
                                               align_corners=False)
                combined_feature = 0.01 * normalized_features_from_net[key] + 1 * features_from_backbone[key]
                combined_features[key] = combined_feature

        # 使用 iCaRLNet 处理加权后的特征图
        features_from_icalnet = self.icalnet(combined_features)

        # 将 iCaRLNet 处理后的特征图与原始加权特征图按权重加权连接
        final_features = {}
        for key in combined_features.keys():
            if key in features_from_icalnet:
                final_feature = 1 * combined_features[key] + 0.01 * features_from_icalnet[key]
                final_features[key] = final_feature

        if detected_instances is None:
            if self.proposal_generator is not None:
                proposals, _ = self.proposal_generator(images, final_features, None)
            else:
                assert "proposals" in batched_inputs[0]
                proposals = [x["proposals"].to(self.device) for x in batched_inputs]

            results, _ = self.roi_heads(images, final_features, proposals, None)
        else:
            detected_instances = [x.to(self.device) for x in detected_instances]
            results = self.roi_heads.forward_with_given_boxes(final_features, detected_instances)

        if do_postprocess:
            assert not torch.jit.is_scripting(), "Scripting is not supported for postprocess."
            return GeneralizedRCNN._postprocess(results, batched_inputs, images.image_sizes)
        return results


    def preprocess_image(self, batched_inputs: List[Dict[str, torch.Tensor]]):
        """
        Normalize, pad and batch the input images.
        """
        images = [self._move_to_current_device(x["image"]) for x in batched_inputs]
        images = [(x - self.pixel_mean) / self.pixel_std for x in images]
        images = ImageList.from_tensors(
            images,
            self.backbone.size_divisibility,
            padding_constraints=self.backbone.padding_constraints,
        )
        return images

    @staticmethod
    def _postprocess(instances, batched_inputs: List[Dict[str, torch.Tensor]], image_sizes):
        """
        Rescale the output instances to the target size.
        """
        # note: private function; subject to changes
        processed_results = []
        for results_per_image, input_per_image, image_size in zip(
            instances, batched_inputs, image_sizes
        ):
            height = input_per_image.get("height", image_size[0])
            width = input_per_image.get("width", image_size[1])
            r = detector_postprocess(results_per_image, height, width)
            processed_results.append({"instances": r})
        return processed_results

    def generate_boundary_data(self, num_points=100):
        # 生成边界点 (x, y) 在边界上
        x = np.random.uniform(0, 1, num_points)
        y = np.zeros(num_points)  # 假设 y=0 是边界
        c = np.sin(np.pi * x)  # 假设边界条件为 sin(pi * x)

        # 将 numpy 数组转换为 torch 张量，并确保形状为 (num_points, 1)
        xb = torch.tensor(x, dtype=torch.float32, device=self.device).unsqueeze(1)
        yb = torch.tensor(y, dtype=torch.float32, device=self.device).unsqueeze(1)
        cb = torch.tensor(c, dtype=torch.float32, device=self.device).unsqueeze(1)

        return xb, yb, cb




@META_ARCH_REGISTRY.register()
class ProposalNetwork(nn.Module):
    """
    A meta architecture that only predicts object proposals.
    """

    @configurable
    def __init__(
        self,
        *,
        backbone: Backbone,
        proposal_generator: nn.Module,
        pixel_mean: Tuple[float],
        pixel_std: Tuple[float],
    ):
        """
        Args:
            backbone: a backbone module, must follow detectron2's backbone interface
            proposal_generator: a module that generates proposals using backbone features
            pixel_mean, pixel_std: list or tuple with #channels element, representing
                the per-channel mean and std to be used to normalize the input image
        """
        super().__init__()
        self.backbone = backbone
        self.proposal_generator = proposal_generator
        self.register_buffer("pixel_mean", torch.tensor(pixel_mean).view(-1, 1, 1), False)
        self.register_buffer("pixel_std", torch.tensor(pixel_std).view(-1, 1, 1), False)

    @classmethod
    def from_config(cls, cfg):
        backbone = build_backbone(cfg)
        return {
            "backbone": backbone,
            "proposal_generator": build_proposal_generator(cfg, backbone.output_shape()),
            "pixel_mean": cfg.MODEL.PIXEL_MEAN,
            "pixel_std": cfg.MODEL.PIXEL_STD,
        }

    @property
    def device(self):
        return self.pixel_mean.device

    def _move_to_current_device(self, x):
        return move_device_like(x, self.pixel_mean)

    def forward(self, batched_inputs):
        """
        Args:
            Same as in :class:`GeneralizedRCNN.forward`

        Returns:
            list[dict]:
                Each dict is the output for one input image.
                The dict contains one key "proposals" whose value is a
                :class:`Instances` with keys "proposal_boxes" and "objectness_logits".
        """
        images = [self._move_to_current_device(x["image"]) for x in batched_inputs]
        images = [(x - self.pixel_mean) / self.pixel_std for x in images]
        images = ImageList.from_tensors(
            images,
            self.backbone.size_divisibility,
            padding_constraints=self.backbone.padding_constraints,
        )
        features = self.backbone(images.tensor)

        if "instances" in batched_inputs[0]:
            gt_instances = [x["instances"].to(self.device) for x in batched_inputs]
        elif "targets" in batched_inputs[0]:
            log_first_n(
                logging.WARN, "'targets' in the model inputs is now renamed to 'instances'!", n=10
            )
            gt_instances = [x["targets"].to(self.device) for x in batched_inputs]
        else:
            gt_instances = None
        proposals, proposal_losses = self.proposal_generator(images, features, gt_instances)
        # In training, the proposals are not useful at all but we generate them anyway.
        # This makes RPN-only models about 5% slower.
        if self.training:
            return proposal_losses

        processed_results = []
        for results_per_image, input_per_image, image_size in zip(
            proposals, batched_inputs, images.image_sizes
        ):
            height = input_per_image.get("height", image_size[0])
            width = input_per_image.get("width", image_size[1])
            r = detector_postprocess(results_per_image, height, width)
            processed_results.append({"proposals": r})
        return processed_results
