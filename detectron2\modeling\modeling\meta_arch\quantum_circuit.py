# quantum_circuit.py
import pennylane as qml
from pennylane import numpy as np
import torch
import torch.nn.functional as F  # 确保导入 F
import warnings
warnings.filterwarnings("ignore", category=UserWarning, message="Requested adjoint differentiation to be computed with finite shots.")


class QuanConvCircuit:

    def __init__(self, n_qubits=4, shots=1, threshold=127, device="lightning.gpu"):
        self.n_qubits = n_qubits
        self.device = qml.device("lightning.gpu", wires=self.n_qubits, shots=shots)
        self.threshold = threshold

        @qml.qnode(self.device, interface="torch", diff_method="adjoint")
        def circuit(inputs):
            for i in range(self.n_qubits):
                qml.RX(inputs[i], wires=i)
            return [qml.expval(qml.PauliZ(i)) for i in range(self.n_qubits)]

        self.circuit = circuit

    def run(self, data):
        # 将数据映射到固定的量子比特数
        data = torch.flatten(data)
        if data.numel() > self.n_qubits:
            # 使用平均池化或其他方法将数据降维到 n_qubits
            data = F.adaptive_avg_pool1d(data.unsqueeze(0).unsqueeze(0), self.n_qubits).squeeze()

        if data.numel() != self.n_qubits:
            raise ValueError(f"Input data size {data.numel()} does not match expected n_qubits {self.n_qubits}")

        thetas = torch.where(data > self.threshold, torch.tensor(np.pi, device=data.device),
                             torch.tensor(0.0, device=data.device))
        probabilities = self.circuit(thetas)
        probabilities = (torch.sum(probabilities) + self.n_qubits) / (2 * self.n_qubits)
        return torch.tensor([probabilities], device=data.device)
