# Makefile for IEEE TGRS Paper Compilation

# Variables
MAIN = ieee_tgrs_paper
TEX = $(MAIN).tex
PDF = $(MAIN).pdf
BIB = ieee_tgrs_references.bib

# Default target
all: $(PDF)

# Compile PDF
$(PDF): $(TEX) $(BIB)
	@echo "Compiling IEEE TGRS paper..."
	pdflatex $(MAIN)
	bibtex $(MAIN)
	pdflatex $(MAIN)
	pdflatex $(MAIN)
	@echo "Compilation completed: $(PDF)"

# Clean temporary files
clean:
	@echo "Cleaning temporary files..."
	rm -f *.aux *.bbl *.blg *.log *.out *.toc *.lof *.lot *.fls *.fdb_latexmk *.synctex.gz
	@echo "Cleanup completed."

# Clean all generated files including PDF
distclean: clean
	rm -f $(PDF)
	@echo "All generated files removed."

# Quick compile (single pass)
quick:
	pdflatex $(MAIN)

# View PDF (Linux/Mac)
view: $(PDF)
	@if command -v evince >/dev/null 2>&1; then \
		evince $(PDF) & \
	elif command -v open >/dev/null 2>&1; then \
		open $(PDF); \
	else \
		echo "No PDF viewer found. Please open $(PDF) manually."; \
	fi

# Check for required files
check:
	@echo "Checking required files..."
	@if [ -f $(TEX) ]; then echo "✓ $(TEX) found"; else echo "✗ $(TEX) missing"; fi
	@if [ -f $(BIB) ]; then echo "✓ $(BIB) found"; else echo "✗ $(BIB) missing"; fi
	@if [ -d images ]; then echo "✓ images/ directory found"; else echo "! images/ directory not found (create if needed)"; fi

# Help
help:
	@echo "Available targets:"
	@echo "  all       - Compile the complete paper (default)"
	@echo "  quick     - Quick compile (single pass)"
	@echo "  clean     - Remove temporary files"
	@echo "  distclean - Remove all generated files"
	@echo "  view      - Open PDF in viewer"
	@echo "  check     - Check for required files"
	@echo "  help      - Show this help message"

.PHONY: all clean distclean quick view check help
