python train.py --config-file /root/detectron2/configs/COCO-InstanceSegmentation/mask_rcnn_R_50_FPN_3x.yaml tensorboard --logdir=/path/to/output/directory /detectron2/demo# python demo.py  --input "/root/detectron2/coco2019/test2019/*.png" --output "/root/detectron2/demo/result1/" --confidence-threshold 0


/detectron2/demo# python demo.py  --input "/root/autodl-tmp/one/*.tif" --output "/root/autodl-tmp/p/" --confidence-threshold 0.7



python train.py /root/mmdetection/configs/mask_rcnn_r50_caffe_fpn_mstrain-poly_1x_balloon.py --work-dir /root/autodl-tmp/swin --cfg-options model.backbone.depth=101