# CL.py
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np


class MemoryBuffer:
    def __init__(self, max_size=200, device='cpu', num_prototypes=10):
        """增强的内存缓冲区，支持原型聚类和重要度采样
        
        Args:
            max_size: 缓冲区最大容量
            device: 计算设备
            num_prototypes: 原型的数量
        """
        self.buffer = []
        self.max_size = max_size
        self.device = device
        self.num_prototypes = num_prototypes
        
        # 原型向量 - 用于聚类特征
        self.prototypes = None
        
        # 缓存特征的重要度分数
        self.importance_scores = []
        
    def _compute_embedding(self, feature_dict):
        """计算特征嵌入，用于聚类和相似度计算"""
        embeddings = {}
        for key, feature in feature_dict.items():
            # 使用全局平均池化作为特征表示
            embedding = F.adaptive_avg_pool2d(feature, 1).view(feature.size(0), -1)
            embeddings[key] = embedding
        return embeddings
    
    def _compute_importance(self, feature_dict):
        """计算特征的重要度分数"""
        # 使用特征图的方差作为重要度指标
        importance = 0
        for feature in feature_dict.values():
            # 计算每个通道的方差，然后平均
            channel_variance = torch.var(feature, dim=(2, 3)).mean()
            importance += channel_variance.item()
        return importance
        
    def _update_prototypes(self):
        """更新内存中的原型，使用K均值聚类"""
        if len(self.buffer) < self.num_prototypes:
            return
        
        # 收集所有特征的平均嵌入
        all_embeddings = []
        for feature_dict in self.buffer:
            embeddings = self._compute_embedding(feature_dict)
            # 计算平均嵌入向量
            avg_embedding = torch.mean(torch.stack([emb.mean(0) for emb in embeddings.values()]), dim=0)
            all_embeddings.append(avg_embedding)
        
        # 转换为单个张量
        all_embeddings = torch.stack(all_embeddings)
        
        # 使用K均值聚类算法
        if self.prototypes is None:
            # 随机初始化原型
            indices = torch.randperm(len(all_embeddings))[:self.num_prototypes]
            self.prototypes = all_embeddings[indices].clone()
        
        # 执行K均值聚类迭代
        for _ in range(5):  # 5次迭代
            # 分配样本到最近的原型
            distances = torch.cdist(all_embeddings, self.prototypes)
            closest_prototype = torch.argmin(distances, dim=1)
            
            # 更新原型
            for i in range(self.num_prototypes):
                cluster_samples = all_embeddings[closest_prototype == i]
                if len(cluster_samples) > 0:
                    self.prototypes[i] = cluster_samples.mean(0)

    def add(self, feature):
        """将新特征添加到内存缓冲区，根据重要度进行选择性保留"""
        if not isinstance(feature, dict):
            raise ValueError("特征必须是字典")
            
        # 计算特征的重要度
        importance = self._compute_importance(feature)
        
        # 将特征移动到设备上，不保留计算图
        feature_on_device = {k: v.detach().to(self.device) for k, v in feature.items()}
        
        if len(self.buffer) >= self.max_size:
            # 如果缓冲区已满，决定是否替换
            if len(self.importance_scores) > 0 and importance > min(self.importance_scores):
                # 找到最不重要的特征索引
                min_idx = self.importance_scores.index(min(self.importance_scores))
                # 替换最不重要的特征
                self.buffer[min_idx] = feature_on_device
                self.importance_scores[min_idx] = importance
            # 如果新特征不够重要，则不添加
        else:
            # 缓冲区未满，直接添加
            self.buffer.append(feature_on_device)
            self.importance_scores.append(importance)
            
        # 每添加10个特征更新一次原型
        if len(self.buffer) % 10 == 0:
            self._update_prototypes()

    def sample(self, num_samples, strategy='diverse'):
        """从内存缓冲区中取样本
        
        Args:
            num_samples: 要取的样本数量
            strategy: 采样策略，'diverse'表示多样性采样，'recent'表示最近添加，
                      'important'表示按重要度，'prototype'表示基于原型
        """
        if len(self.buffer) == 0:
            return []
            
        num_samples = min(num_samples, len(self.buffer))
        
        if strategy == 'recent':
            # 最近添加的样本
            return self.buffer[-num_samples:]
            
        elif strategy == 'important':
            # 按重要度排序，取最重要的样本
            sorted_indices = sorted(range(len(self.importance_scores)), 
                                    key=lambda i: self.importance_scores[i], 
                                    reverse=True)
            return [self.buffer[i] for i in sorted_indices[:num_samples]]
            
        elif strategy == 'prototype' and self.prototypes is not None:
            # 根据原型采样，确保覆盖所有原型
            samples = []
            prototype_counts = torch.zeros(self.num_prototypes)
            
            # 为每个特征计算到原型的距离
            for i, feature_dict in enumerate(self.buffer):
                embeddings = self._compute_embedding(feature_dict)
                avg_embedding = torch.mean(torch.stack([emb.mean(0) for emb in embeddings.values()]), dim=0)
                
                distances = torch.norm(self.prototypes - avg_embedding.unsqueeze(0), dim=1)
                closest_prototype = torch.argmin(distances).item()
                
                # 优先选择还未被采样的原型
                if prototype_counts[closest_prototype] == 0:
                    samples.append(feature_dict)
                    prototype_counts[closest_prototype] += 1
                    
                if len(samples) >= num_samples:
                    break
                    
            # 如果还没选够，根据原型数量比例选择
            if len(samples) < num_samples:
                remaining = num_samples - len(samples)
                # 为每个原型计算还需要多少样本
                remaining_per_prototype = torch.floor(torch.ones(self.num_prototypes) * remaining / self.num_prototypes)
                
                for i, feature_dict in enumerate(self.buffer):
                    if feature_dict in samples:
                        continue
                        
                    embeddings = self._compute_embedding(feature_dict)
                    avg_embedding = torch.mean(torch.stack([emb.mean(0) for emb in embeddings.values()]), dim=0)
                    
                    distances = torch.norm(self.prototypes - avg_embedding.unsqueeze(0), dim=1)
                    closest_prototype = torch.argmin(distances).item()
                    
                    if prototype_counts[closest_prototype] < remaining_per_prototype[closest_prototype]:
                        samples.append(feature_dict)
                        prototype_counts[closest_prototype] += 1
                        
                    if len(samples) >= num_samples:
                        break
                        
            return samples
            
        else:  # strategy == 'diverse' 或其他
            # 默认使用多样性采样，尽量选择差异较大的样本
            samples = [self.buffer[0]]  # 从第一个样本开始
            
            if len(self.buffer) > 1:
                # 计算所有特征的嵌入
                all_embeddings = []
                for feature_dict in self.buffer:
                    embeddings = self._compute_embedding(feature_dict)
                    avg_embedding = torch.mean(torch.stack([emb.mean(0) for emb in embeddings.values()]), dim=0)
                    all_embeddings.append(avg_embedding)
                
                # 贪婪选择最多样化的样本集
                while len(samples) < num_samples:
                    # 计算已选样本的平均嵌入
                    selected_indices = [self.buffer.index(s) for s in samples]
                    selected_embeddings = torch.stack([all_embeddings[i] for i in selected_indices])
                    
                    # 计算每个未选样本到已选样本的最小距离
                    max_min_distance = -1
                    next_sample = None
                    
                    for i, feature_dict in enumerate(self.buffer):
                        if feature_dict in samples:
                            continue
                            
                        # 计算到已选样本的距离
                        embedding = all_embeddings[i]
                        distances = torch.norm(selected_embeddings - embedding.unsqueeze(0), dim=1)
                        min_distance = torch.min(distances).item()
                        
                        if min_distance > max_min_distance:
                            max_min_distance = min_distance
                            next_sample = feature_dict
                    
                    if next_sample is not None:
                        samples.append(next_sample)
                    else:
                        # 如果无法找到更多样的样本，随机添加
                        remaining = [f for f in self.buffer if f not in samples]
                        samples.extend(remaining[:num_samples-len(samples)])
                        break
            
            return samples


class FeatureAttention(nn.Module):
    """特征注意力模块，增强判别性特征"""
    def __init__(self, channel, reduction=16):
        super(FeatureAttention, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
        
        self.fc = nn.Sequential(
            nn.Conv2d(2, 1, 1, bias=False),
            nn.ReLU(),
            nn.Conv2d(channel, channel // reduction, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(channel // reduction, channel, 1, bias=False),
            nn.Sigmoid()
        )
        
    def forward(self, x):
        # 创建通道和空间注意力
        avg_out = self.avg_pool(x)
        max_out = self.max_pool(x)
        # 沿着通道维度连接
        channel_out = torch.cat([avg_out, max_out], dim=1)
        channel_attn = self.fc(channel_out)
        
        return x * channel_attn


class iCaRLNet(nn.Module):
    def __init__(self, device='cpu', initial_a=0.01, max_a=1.0, memory_size=200, num_prototypes=10):
        super(iCaRLNet, self).__init__()
        self.device = device
        self.a = initial_a  # 初始因子 a，控制旧特征的使用比例
        self.max_a = max_a  # 最大因子 a
        
        # 特征增强模块 - 为每个特征级别创建一个注意力模块
        self.attention_modules = nn.ModuleDict({
            'p2': FeatureAttention(256),
            'p3': FeatureAttention(256),
            'p4': FeatureAttention(256),
            'p5': FeatureAttention(256),
            'p6': FeatureAttention(256)
        })
        
        # 特征融合网络 - 增强特征表示
        self.fusion_layers = nn.ModuleDict({
            'p2': nn.Conv2d(256, 256, kernel_size=1),
            'p3': nn.Conv2d(256, 256, kernel_size=1),
            'p4': nn.Conv2d(256, 256, kernel_size=1),
            'p5': nn.Conv2d(256, 256, kernel_size=1),
            'p6': nn.Conv2d(256, 256, kernel_size=1)
        })
        
        # 内存缓冲区
        self.memory_buffer = MemoryBuffer(
            max_size=memory_size,
            device=device,
            num_prototypes=num_prototypes
        )
        
        # 将模块移动到设备
        self.to(device)
        
    def forward(self, features, update_memory=True, use_old_features=True, sampling_strategy='diverse'):
        """
        处理输入特征并与记忆中的旧特征融合
        
        Args:
            features: 输入特征字典，从backbone生成
            update_memory: 是否更新内存
            use_old_features: 是否使用旧特征
            sampling_strategy: 采样策略
            
        Returns:
            enhanced_features: 增强后的特征字典
        """
        enhanced_features = {}
        
        # 1. 为每个特征层级应用注意力和融合
        for key, feature in features.items():
            if key in self.attention_modules:
                # 应用注意力机制增强特征
                enhanced = self.attention_modules[key](feature)
                # 应用融合层
                enhanced = self.fusion_layers[key](enhanced)
                # 添加残差连接
                enhanced_features[key] = enhanced + feature
            else:
                enhanced_features[key] = feature
                
        # 2. 如果需要，将当前特征与旧特征融合
        if use_old_features:
            # 计算要抽取的旧特征数量
            num_samples = int(min(100, self.memory_buffer.max_size) * self.a)
            if num_samples > 0:
                # 从内存中采样旧特征
                old_features = self.memory_buffer.sample(
                    num_samples=num_samples,
                    strategy=sampling_strategy
                )
                
                # 将旧特征与当前特征融合
                enhanced_features = self._fuse_with_old_features(enhanced_features, old_features)
        
        # 3. 可选：更新内存
        if update_memory:
            self.update_memory_buffer(enhanced_features)
            
        return enhanced_features
    
    def _fuse_with_old_features(self, current_features, old_features_list):
        """
        将当前特征与旧特征融合
        
        Args:
            current_features: 当前增强的特征
            old_features_list: 旧特征列表
            
        Returns:
            fused_features: 融合后的特征
        """
        if not old_features_list:
            return current_features
            
        fused_features = {}
        
        for key in current_features.keys():
            x_current = current_features[key]
            
            # 收集所有有效的旧特征
            old_features = []
            for old_feat_dict in old_features_list:
                if key in old_feat_dict:
                    old_feat = old_feat_dict[key].to(x_current.device)
                    # 确保尺寸匹配
                    if old_feat.shape[2:] != x_current.shape[2:]:
                        old_feat = F.interpolate(old_feat, 
                                               size=x_current.shape[2:],
                                               mode='bilinear',
                                               align_corners=False)
                    old_features.append(old_feat)
            
            if old_features:
                # 自适应加权融合
                all_features = [x_current] + old_features
                
                # 计算注意力权重 - 基于特征相似性
                weights = []
                for feat in old_features:
                    # 计算余弦相似度
                    similarity = F.cosine_similarity(
                        x_current.view(x_current.size(0), -1),
                        feat.view(feat.size(0), -1),
                        dim=1
                    ).mean()
                    weights.append(max(0, similarity.item()))  # 仅使用正相似度
                
                # 添加当前特征的权重
                weights = [1.0] + weights
                
                # 归一化权重
                total_weight = sum(weights) + 1e-5
                weights = [w / total_weight for w in weights]
                
                # 加权融合
                fused = x_current * weights[0]
                for i, feat in enumerate(old_features):
                    fused = fused + feat * weights[i+1]
                    
                fused_features[key] = fused
            else:
                fused_features[key] = x_current
                
        return fused_features
    
    def update_memory_buffer(self, features):
        """更新内存缓冲区"""
        # 复制特征，不保留计算图
        features_copy = {k: v.detach().cpu() for k, v in features.items()}
        self.memory_buffer.add(features_copy)
        
    def update_factor_a(self, a):
        """更新控制旧特征使用比例的因子 a"""
        self.a = min(max(a, 0.0), self.max_a)
