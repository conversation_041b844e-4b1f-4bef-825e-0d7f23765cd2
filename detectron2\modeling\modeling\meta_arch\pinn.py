import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np


class Swish(nn.Module):
    def __init__(self, inplace=True):
        super(Swish, self).__init__()
        self.inplace = inplace

    def forward(self, x):
        if self.inplace:
            x.mul_(torch.sigmoid(x))
            return x
        else:
            return x * torch.sigmoid(x)


class Net2(nn.Module):
    def __init__(self, input_n=2, h_n=32, depth=4):
        super(Net2, self).__init__()
        self.depth = depth
        
        # 初始层
        self.layers = nn.ModuleList()
        self.layers.append(nn.Linear(input_n, h_n))
        self.activations = nn.ModuleList()
        self.activations.append(Swish())
        
        # 隐藏层
        for i in range(depth-2):
            self.layers.append(nn.Linear(h_n, h_n))
            # 交替使用Swish和GELU激活函数
            if i % 2 == 0:
                self.activations.append(Swish())
            else:
                self.activations.append(nn.GELU())
                
        # 输出层
        self.layers.append(nn.Linear(h_n, 1))
        
        # 初始化权重
        self._init_weights()
        
    def _init_weights(self):
        for i in range(self.depth-1):
            nn.init.xavier_normal_(self.layers[i].weight)
            if self.layers[i].bias is not None:
                nn.init.zeros_(self.layers[i].bias)
        # 输出层使用较小的初始化权重
        nn.init.xavier_normal_(self.layers[-1].weight, gain=0.1)
        nn.init.zeros_(self.layers[-1].bias)

    def forward(self, x):
        for i in range(self.depth-1):
            x = self.activations[i](self.layers[i](x))
        output = self.layers[-1](x)
        return output


class PINN(nn.Module):
    def __init__(self, input_n=2, h_n=32, depth=4, diff_coeff=0.1, reaction_term=-1.0, 
                 lambda_bc=1.0, lambda_pde=1.0, pde_type='diffusion-reaction'):
        super(PINN, self).__init__()
        # 物理参数
        self.diff_coeff = diff_coeff  # 扩散系数
        self.reaction_term = reaction_term  # 反应项系数
        
        # 损失权重
        self.lambda_bc = lambda_bc  # 边界条件损失权重
        self.lambda_pde = lambda_pde  # PDE损失权重
        
        # PDE类型: 'diffusion', 'diffusion-reaction', 'convection-diffusion'
        self.pde_type = pde_type
        
        # 若选择对流扩散方程，设置对流速度向量
        if pde_type == 'convection-diffusion':
            self.v_x = nn.Parameter(torch.tensor(0.5), requires_grad=True)
            self.v_y = nn.Parameter(torch.tensor(0.5), requires_grad=True)
        
        # 神经网络核心
        self.net2 = Net2(input_n=input_n, h_n=h_n, depth=depth)

    def forward(self, x, y):
        """标准前向传播"""
        # 确保输入张量与模型参数在同一个设备上
        device = next(self.parameters()).device
        x, y = x.to(device), y.to(device)
        net_in = torch.cat((x, y), 1)
        C = self.net2(net_in)
        return C.view(len(C), -1)
    
    def compute_derivatives(self, x, y):
        """计算求解函数的偏导数"""
        device = next(self.parameters()).device
        x, y = x.to(device), y.to(device)
        x.requires_grad = True
        y.requires_grad = True
        
        net_in = torch.cat((x, y), 1)
        C = self.net2(net_in)
        C = C.view(len(C), -1)
        
        # 一阶导数
        c_x = torch.autograd.grad(C, x, grad_outputs=torch.ones_like(x), 
                                create_graph=True, only_inputs=True)[0]
        c_y = torch.autograd.grad(C, y, grad_outputs=torch.ones_like(y), 
                                create_graph=True, only_inputs=True)[0]
        
        # 二阶导数
        c_xx = torch.autograd.grad(c_x, x, grad_outputs=torch.ones_like(x), 
                                 create_graph=True, only_inputs=True)[0]
        c_yy = torch.autograd.grad(c_y, y, grad_outputs=torch.ones_like(y), 
                                 create_graph=True, only_inputs=True)[0]
        
        return C, c_x, c_y, c_xx, c_yy

    def criterion(self, x, y):
        """PDE损失函数"""
        # 计算偏导数
        C, c_x, c_y, c_xx, c_yy = self.compute_derivatives(x, y)
        
        # 拉普拉斯算子: Δc = c_xx + c_yy
        laplacian = c_xx + c_yy
        
        # 根据PDE类型构建残差
        if self.pde_type == 'diffusion':
            # 扩散方程: ∂c/∂t = D·Δc
            residual = - self.diff_coeff * laplacian
            
        elif self.pde_type == 'diffusion-reaction':
            # 扩散反应方程: ∂c/∂t = D·Δc + f
            residual = self.reaction_term - self.diff_coeff * laplacian
            
        elif self.pde_type == 'convection-diffusion':
            # 对流扩散方程: ∂c/∂t + v·∇c = D·Δc
            convection_term = self.v_x * c_x + self.v_y * c_y
            residual = convection_term - self.diff_coeff * laplacian
        
        else:
            raise ValueError(f"未支持的PDE类型: {self.pde_type}")
        
        # 计算PDE残差的MSE损失
        loss_f = nn.MSELoss()
        loss = loss_f(residual, torch.zeros_like(residual))
        return loss * self.lambda_pde

    def Loss_BC(self, xb, yb, cb, bc_type='dirichlet'):
        """边界条件损失函数，支持多种边界条件类型"""
        device = next(self.parameters()).device
        xb = xb.to(device)
        yb = yb.to(device)
        cb = cb.to(device)
        
        if bc_type == 'dirichlet':
            # 狄利克雷边界条件: u = g 在边界上
            c_pred = self.forward(xb, yb)
            loss_f = nn.MSELoss()
            loss_bc = loss_f(c_pred, cb)
            
        elif bc_type == 'neumann':
            # 诺伊曼边界条件: ∂u/∂n = g 在边界上
            # 计算法向导数 (这里假设对y方向的导数)
            xb.requires_grad = True
            yb.requires_grad = True
            c_pred = self.forward(xb, yb)
            
            # 计算梯度
            c_y = torch.autograd.grad(c_pred, yb, grad_outputs=torch.ones_like(yb), 
                                    create_graph=True, only_inputs=True)[0]
            
            loss_f = nn.MSELoss()
            loss_bc = loss_f(c_y, cb)  # cb现在表示法向导数值
            
        elif bc_type == 'robin':
            # 罗宾边界条件: ∂u/∂n + α·u = g 在边界上
            # 这里α是一个标量参数，需要额外传入
            alpha = 0.1  # 可以修改为参数
            
            xb.requires_grad = True
            yb.requires_grad = True
            c_pred = self.forward(xb, yb)
            
            # 计算法向导数
            c_y = torch.autograd.grad(c_pred, yb, grad_outputs=torch.ones_like(yb), 
                                    create_graph=True, only_inputs=True)[0]
            
            # 罗宾条件
            robin_term = c_y + alpha * c_pred
            
            loss_f = nn.MSELoss()
            loss_bc = loss_f(robin_term, cb)
            
        else:
            raise ValueError(f"不支持的边界条件类型: {bc_type}")
        
        return loss_bc * self.lambda_bc

    def inference(self, feature):
        """用训练好的PINN进行推理，增强特征图"""
        device = next(self.parameters()).device
        b, c, h, w = feature.shape
        
        # 调整特征大小以减少计算量
        feature = F.interpolate(feature, size=(64, 64), mode='bilinear', align_corners=False)
        
        # 生成网格坐标
        x_range = torch.linspace(0, 1, feature.shape[2], device=device)
        y_range = torch.linspace(0, 1, feature.shape[3], device=device)
        grid_x, grid_y = torch.meshgrid(x_range, y_range, indexing='ij')
        
        # 展平坐标以便批处理
        x_flat = grid_x.reshape(-1, 1)
        y_flat = grid_y.reshape(-1, 1)
        
        # 前向传播计算PINN输出
        with torch.no_grad():
            C_flat = self.forward(x_flat, y_flat)
        
        # 重塑为2D
        C_2d = C_flat.reshape(feature.shape[2], feature.shape[3])
        
        # 扩展为与原始特征相同的批次和通道
        C_expanded = C_2d.unsqueeze(0).unsqueeze(0).repeat(b, c, 1, 1)
        
        # 上采样回原始特征大小
        C_upsampled = F.interpolate(C_expanded, size=(h, w), mode='bilinear', align_corners=False)
        
        # 融合: 使用PINN输出作为增强项
        enhanced_feature = feature + 0.1 * C_upsampled
        
        return enhanced_feature
        
    def generate_boundary_data(self, num_points=100, domain_type='square'):
        """生成更灵活的边界条件数据"""
        device = next(self.parameters()).device
        
        if domain_type == 'square':
            # 为正方形区域生成边界点
            n_per_side = num_points // 4
            
            # 下边界: y=0, x∈[0,1]
            x_bottom = torch.linspace(0, 1, n_per_side, device=device)
            y_bottom = torch.zeros_like(x_bottom, device=device)
            
            # 右边界: x=1, y∈[0,1]
            y_right = torch.linspace(0, 1, n_per_side, device=device)
            x_right = torch.ones_like(y_right, device=device)
            
            # 上边界: y=1, x∈[0,1]
            x_top = torch.linspace(1, 0, n_per_side, device=device)  # 从右到左
            y_top = torch.ones_like(x_top, device=device)
            
            # 左边界: x=0, y∈[0,1]
            y_left = torch.linspace(1, 0, n_per_side, device=device)  # 从上到下
            x_left = torch.zeros_like(y_left, device=device)
            
            # 合并所有边界点
            x_boundary = torch.cat([x_bottom, x_right, x_top, x_left])
            y_boundary = torch.cat([y_bottom, y_right, y_top, y_left])
            
            # 生成边界条件值
            # 这里使用了一个更复杂的分段边界条件
            c_boundary = torch.zeros_like(x_boundary, device=device)
            
            # 下边界条件: sin(2πx)
            c_boundary[:n_per_side] = torch.sin(2 * np.pi * x_bottom)
            
            # 右边界条件: cos(πy)
            c_boundary[n_per_side:2*n_per_side] = torch.cos(np.pi * y_right)
            
            # 上边界条件: sin(πx)
            c_boundary[2*n_per_side:3*n_per_side] = torch.sin(np.pi * x_top)
            
            # 左边界条件: exp(-2y)
            c_boundary[3*n_per_side:] = torch.exp(-2 * y_left)
            
        elif domain_type == 'circle':
            # 为圆形区域生成边界点
            theta = torch.linspace(0, 2*np.pi, num_points, device=device)
            radius = 0.5
            center_x, center_y = 0.5, 0.5
            
            x_boundary = center_x + radius * torch.cos(theta)
            y_boundary = center_y + radius * torch.sin(theta)
            
            # 圆形边界条件: cos(θ)
            c_boundary = torch.cos(theta)
            
        else:
            raise ValueError(f"未支持的区域类型: {domain_type}")
        
        # 重塑为列向量
        x_boundary = x_boundary.view(-1, 1)
        y_boundary = y_boundary.view(-1, 1)
        c_boundary = c_boundary.view(-1, 1)
        
        return x_boundary, y_boundary, c_boundary
