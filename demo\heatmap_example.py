import numpy as np
import matplotlib.pyplot as plt
import cv2

# 创建示例图像
image_size = (300, 400, 3)
background = np.ones(image_size, dtype=np.uint8) * 240  # 浅灰色背景

# 创建两个示例实例区域（圆形）
mask1 = np.zeros((image_size[0], image_size[1]), dtype=np.uint8)
mask2 = np.zeros((image_size[0], image_size[1]), dtype=np.uint8)

# 创建圆形mask
cv2.circle(mask1, (150, 100), 50, 1, -1)  # 实例1
cv2.circle(mask2, (250, 200), 70, 1, -1)  # 实例2

# 转换为bool类型
mask1 = mask1.astype(bool)
mask2 = mask2.astype(bool)

# 创建示例ExG值
exg_map = np.zeros((image_size[0], image_size[1]), dtype=np.float32)
y, x = np.ogrid[:image_size[0], :image_size[1]]

# 为实例1创建渐变ExG值（0.2-0.4）
dist1 = np.sqrt((x - 150)**2 + (y - 100)**2)
exg_map[mask1] = np.clip(0.4 - dist1[mask1]/250, 0.2, 0.4)

# 为实例2创建渐变ExG值（0.6-0.8）
dist2 = np.sqrt((x - 250)**2 + (y - 200)**2)
exg_map[mask2] = np.clip(0.8 - dist2[mask2]/250, 0.6, 0.8)

# 创建热力图
plt.figure(figsize=(15, 5))

# 原图
plt.subplot(131)
plt.imshow(background)
plt.title('Original Image')
plt.axis('off')

# 带数值的ExG图
plt.subplot(132)
plt.imshow(exg_map, cmap='RdYlGn', vmin=0, vmax=1)
plt.colorbar(label='Vegetation Index')
plt.title('ExG Values')
plt.axis('off')

# 混合后的热力图
combined_mask = mask1 | mask2
cmap = plt.cm.RdYlGn
colored_exg = np.zeros_like(background, dtype=np.float32)
colored_exg[combined_mask] = cmap(exg_map[combined_mask])[:, :3]
colored_exg = (colored_exg * 255).astype(np.uint8)

alpha = 0.7
mask_3d = np.stack([combined_mask] * 3, axis=2)
blended = cv2.addWeighted(background, 1-alpha, colored_exg, alpha, 0)
final = np.where(mask_3d, blended, background)

plt.subplot(133)
plt.imshow(final)
plt.title('Blended Heatmap')
plt.axis('off')

# 添加实例编号
font = {'family': 'sans-serif',
        'color':  'white',
        'weight': 'bold',
        'size': 16,
        }
plt.text(150, 100, '1', fontdict=font, ha='center', va='center')
plt.text(250, 200, '2', fontdict=font, ha='center', va='center')

plt.tight_layout()
plt.savefig('D:/deeplearn/detectron2/demo/heatmap_example.png', dpi=300, bbox_inches='tight')
plt.close()
