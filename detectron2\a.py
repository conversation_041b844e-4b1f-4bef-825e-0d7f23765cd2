import os
import mmcv
from mmcv.runner import load_checkpoint
from mmdet.models import build_detector
from mmdet.apis import init_detector, inference_detector

# Configuration and checkpoint files
config_file = '/root/mmdetection/configs/mask_rcnn_r50_caffe_fpn_mstrain-poly_1x_balloon.py'
checkpoint_file = '/root/autodl-tmp/cascadercnn/epoch_1.pth'

# Initialize the model
model = init_detector(config_file, checkpoint_file)

# Directory containing images
img_dir = '/root/mmdetection/coco2019/test2019/'

# Output directory
out_dir = 'results/'
if not os.path.exists(out_dir):
    os.mkdir(out_dir)

# List all image files in the directory
imgs = [os.path.join(img_dir, img) for img in os.listdir(img_dir) if img.endswith('.png')]

results = []
count = 0
for img in imgs:
    count += 1
    print('Model is processing the {}/{} images.'.format(count, len(imgs)))
    result = inference_detector(model, img)
    results.append(result)

# Save the results
print('\nWriting results to {}'.format('faster_voc.pkl'))
mmcv.dump(results, os.path.join(out_dir, 'faster_voc.pkl'))