_BASE_: "../Base-DensePose-RCNN-FPN.yaml"
MODEL:
  WEIGHTS: "detectron2://ImageNetPretrained/MSRA/R-50.pkl"
  ROI_HEADS:
    NUM_CLASSES: 1
DATASETS:
  TRAIN: ("densepose_coco_2014_minival",)
  TEST: ("densepose_coco_2014_minival",)
SOLVER:
  CLIP_GRADIENTS:
    ENABLED: True
    CLIP_TYPE: norm
    CLIP_VALUE: 1.0
  MAX_ITER: 6000
  STEPS: (5500, 5800)
TEST:
  EXPECTED_RESULTS: [["bbox", "AP", 76.2477, 1.0], ["densepose_gps", "AP", 79.6090, 1.5], ["densepose_gpsm", "AP", 80.0061, 1.5]]

