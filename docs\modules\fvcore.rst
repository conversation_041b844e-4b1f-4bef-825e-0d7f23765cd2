fvcore documentation
====================

Detectron2 depends on utilities in
`fvcore <https://github.com/facebookresearch/fvcore/>`_.
We include part of fvcore documentation here for easier reference.

fvcore.nn
-----------------

.. automodule:: fvcore.nn
    :members:
    :inherited-members:
    :undoc-members:
    :show-inheritance:

fvcore.common
---------------------

.. automodule:: fvcore.common.checkpoint
    :members:
    :undoc-members:
    :show-inheritance:

.. automodule:: fvcore.common.config
    :members:
    :undoc-members:
    :show-inheritance:

.. automodule:: fvcore.common.history_buffer
    :members:
    :undoc-members:
    :show-inheritance:

.. automodule:: fvcore.common.param_scheduler
    :members:
    :inherited-members:
    :undoc-members:
    :show-inheritance:

.. automodule:: fvcore.common.registry
    :members:
    :undoc-members:
    :show-inheritance:

.. automodule:: fvcore.common.timer
    :members:
    :undoc-members:
    :show-inheritance:
