_BASE_: "Base-DensePose-RCNN-FPN.yaml"
MODEL:
  WEIGHTS: "https://dl.fbaipublicfiles.com/densepose/cse/densepose_rcnn_R_50_FPN_soft_s1x/250533982/model_final_2c4512.pkl"
  RESNETS:
    DEPTH: 50
  ROI_DENSEPOSE_HEAD:
    NAME: "DensePoseV1ConvXHead"
    CSE:
      EMBED_LOSS_NAME: "SoftEmbeddingLoss"
      EMBEDDING_DIST_GAUSS_SIGMA: 0.1
      GEODESIC_DIST_GAUSS_SIGMA: 0.1
      EMBEDDERS:
        "chimp_5029":
          TYPE: vertex_feature
          NUM_VERTICES: 5029
          FEATURE_DIM: 256
          FEATURES_TRAINABLE: False
          IS_TRAINABLE: True
          INIT_FILE: "https://dl.fbaipublicfiles.com/densepose/data/cse/lbo/phi_chimp_5029_256.pkl"
DATASETS:
  TRAIN:
    - "densepose_chimps_cse_train"
  TEST:
    - "densepose_chimps_cse_val"
  CLASS_TO_MESH_NAME_MAPPING:
    "0": "chimp_5029"
SOLVER:
  MAX_ITER: 4000
  STEPS: (3000, 3500)
