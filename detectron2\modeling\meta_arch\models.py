# models.py
import torch
import torch.nn as nn
import torch.nn.functional as F
from .mamba import Mamba, ModelArgs
import torch.nn.init as init
from einops import rearrange




class AdaptiveGaussianFilter(nn.Module):
    def __init__(self, in_channels, kernel_size=5, min_sigma=0.5, max_sigma=2.0):
        super(AdaptiveGaussianFilter, self).__init__()
        self.kernel_size = kernel_size
        self.min_sigma = min_sigma
        self.max_sigma = max_sigma
        self.in_channels = in_channels

    def _create_gaussian_kernel(self, sigma):
        # 创建一个标准的高斯核
        x = torch.arange(-self.kernel_size // 2 + 1., self.kernel_size // 2 + 1.)
        y = torch.arange(-self.kernel_size // 2 + 1., self.kernel_size // 2 + 1.)
        xx, yy = torch.meshgrid(x, y)
        kernel = torch.exp(-(xx ** 2 + yy ** 2) / (2. * sigma ** 2))
        kernel /= kernel.sum()
        kernel = kernel.unsqueeze(0).unsqueeze(0).repeat(self.in_channels, 1, 1, 1)
        return kernel

    def _compute_local_variance(self, x):
        # 计算局部区域方差
        padding = self.kernel_size // 2
        mean = F.avg_pool2d(x, self.kernel_size, stride=1, padding=padding)
        squared_mean = F.avg_pool2d(x ** 2, self.kernel_size, stride=1, padding=padding)
        variance = squared_mean - mean ** 2
        return variance

    def forward(self, x):
        # 计算局部区域的方差
        variance = self._compute_local_variance(x)

        # 根据方差动态计算标准差
        sigma = torch.sqrt(torch.clamp(variance, min=0) + 1e-6)  # 方差需要大于0
        sigma = torch.clamp(sigma, min=self.min_sigma, max=self.max_sigma)  # 限制sigma的范围

        # 使用动态的sigma生成高斯核
        kernels = self._create_gaussian_kernel(sigma)

        # 使用自适应的高斯核进行卷积
        output = F.conv2d(x, kernels.to(x.device), padding=self.kernel_size // 2, groups=x.shape[1])
        return output


class ComplexConv2d(nn.Module):
    def __init__(self, in_channels, out_channels, kernel_size, stride=1, padding=0, device='cpu'):
        super(ComplexConv2d, self).__init__()
        self.device = device
        self.real_conv = nn.Conv2d(in_channels, out_channels, kernel_size, stride, padding).to(self.device)
        self.imag_conv = nn.Conv2d(in_channels, out_channels, kernel_size, stride, padding).to(self.device)

    def forward(self, x):
        if x.dtype != torch.complex64:
            x = torch.complex(x, torch.zeros_like(x).to(self.device))  # 转为复数张量
        real_part = self.real_conv(torch.real(x))  # 实部卷积
        imag_part = self.imag_conv(torch.imag(x))  # 虚部卷积
        return torch.complex(real_part, imag_part)



class LaplacianTransform(nn.Module):
    def __init__(self, in_channels, out_channels):
        super(LaplacianTransform, self).__init__()
        # 定义 3x3 和 5x5 的拉普拉斯算子卷积核
        laplacian_kernel_3x3 = torch.tensor([
            [1, 0, 1],
            [0, -4, 0],
            [1, 0, 1]
        ], dtype=torch.float32).unsqueeze(0).unsqueeze(0).repeat(in_channels, 1, 1, 1)

        laplacian_kernel_5x5 = torch.tensor([
            [-2, -4, -4, -4, -2],
            [-4, 0, 8, 0, -4],
            [-4, 8, 24, 8, -4],
            [-4, 0, 8, 0, -4],
            [-2, -4, -4, -4, -2]
        ], dtype=torch.float32).unsqueeze(0).unsqueeze(0).repeat(in_channels, 1, 1, 1)

        self.register_buffer('laplacian_kernel_3x3', laplacian_kernel_3x3)
        self.register_buffer('laplacian_kernel_5x5', laplacian_kernel_5x5)

    def forward(self, x):
        # 确保输入是4D张量 (batch_size, channels, height, width)
        if x.dim() == 3:  # 如果输入是3D，增加一个 batch_size 维度
            x = x.unsqueeze(0)
            
        # 将输入转换为复数张量
        x_complex = torch.fft.fft2(x)
        
        # 提取实部和虚部，进行卷积操作
        real_part = torch.real(x_complex)
        imag_part = torch.imag(x_complex)

        # 使用 3x3 和 5x5 的拉普拉斯算子进行卷积操作
        laplacian_3x3_real = F.conv2d(real_part, self.laplacian_kernel_3x3, groups=real_part.size(1),
                                      padding=1)  # 使用实部进行卷积
        laplacian_5x5_real = F.conv2d(real_part, self.laplacian_kernel_5x5, groups=real_part.size(1),
                                      padding=2)  # 使用实部进行卷积
        laplacian_3x3_imag = F.conv2d(imag_part, self.laplacian_kernel_3x3, groups=imag_part.size(1),
                                      padding=1)  # 使用虚部进行卷积
        laplacian_5x5_imag = F.conv2d(imag_part, self.laplacian_kernel_5x5, groups=imag_part.size(1),
                                      padding=2)  # 使用虚部进行卷积

        # 将 3x3 和 5x5 的拉普拉斯变换结果合并
        laplacian_real = laplacian_3x3_real + laplacian_5x5_real
        laplacian_imag = laplacian_3x3_imag + laplacian_5x5_imag

        # 合并实部和虚部为一个复数张量
        laplacian_complex = torch.complex(laplacian_real, laplacian_imag)

        # 将复数结果通过逆傅里叶变换转回空间域
        # 或者直接提取复数张量的实部和虚部，然后拼接
        
        # 将实部和虚部合并为一个实数张量
        fourier_combined = torch.cat((laplacian_real, laplacian_imag), dim=1)

        return fourier_combined



class ResidualBlock(nn.Module):
    def __init__(self, in_channels, out_channels, stride=1):
        super(ResidualBlock, self).__init__()
        self.conv1 = nn.Conv2d(in_channels, out_channels, kernel_size=3, stride=stride, padding=1, bias=False)
        self.bn1 = nn.BatchNorm2d(out_channels)
        self.relu = nn.ReLU(inplace=True)
        self.conv2 = nn.Conv2d(out_channels, out_channels, kernel_size=3, stride=1, padding=1, bias=False)
        self.bn2 = nn.BatchNorm2d(out_channels)

        self.shortcut = nn.Sequential()
        if stride != 1 or in_channels != out_channels:
            self.shortcut = nn.Sequential(
                nn.Conv2d(in_channels, out_channels, kernel_size=1, stride=stride, bias=False),
                nn.BatchNorm2d(out_channels)
            )

    def forward(self, x):
        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)
        out = self.conv2(out)
        out = self.bn2(out)
        out += self.shortcut(x)
        out = self.relu(out)
        return out


class Net(nn.Module):
    def __init__(self):
        super(Net, self).__init__()
        self.laplacian = LaplacianTransform(in_channels=3, out_channels=3)  # 使用拉普拉斯变换
        # 使用 ResidualBlock 来处理拉普拉斯变换后的特征
        self.residual1 = ResidualBlock(in_channels=6, out_channels=256, stride=2)
        self.residual2 = ResidualBlock(in_channels=256, out_channels=256, stride=2)
        self.residual3 = ResidualBlock(in_channels=256, out_channels=256, stride=2)
        self.residual4 = ResidualBlock(in_channels=256, out_channels=256, stride=2)
        self.residual5 = ResidualBlock(in_channels=256, out_channels=256, stride=2)
        self.relu = nn.ReLU(inplace=True)

        # 初始化 Mamba 模块
        mamba_args = ModelArgs(d_model=256, n_layer=1, d_state=4, dt_rank=4)
        self.mamba_p2 = Mamba(mamba_args)
        self.mamba_p3 = Mamba(mamba_args)
        self.mamba_p4 = Mamba(mamba_args)
        self.mamba_p5 = Mamba(mamba_args)
        self.mamba_p6 = Mamba(mamba_args)

    def forward(self, x):
        # 应用拉普拉斯变换
        laplacian_output = self.laplacian(x)

        # 应用残差块
        p2 = self.residual1(laplacian_output)
        p3 = self.residual2(p2)
        p4 = self.residual3(p3)
        p5 = self.residual4(p4)
        p6 = self.residual5(p5)

        # 调整形状以适应 Mamba 模块
        p2_flat = rearrange(p2, 'b c h w -> b (h w) c')
        p3_flat = rearrange(p3, 'b c h w -> b (h w) c')
        p4_flat = rearrange(p4, 'b c h w -> b (h w) c')
        p5_flat = rearrange(p5, 'b c h w -> b (h w) c')
        p6_flat = rearrange(p6, 'b c h w -> b (h w) c')

        # 通过 Mamba 模块
        p2_mamba = self.mamba_p2(p2_flat)
        p3_mamba = self.mamba_p3(p3_flat)
        p4_mamba = self.mamba_p4(p4_flat)
        p5_mamba = self.mamba_p5(p5_flat)
        p6_mamba = self.mamba_p6(p6_flat)

        # 调整形状以恢复原始特征图形状
        p2 = rearrange(p2_mamba, 'b (h w) c -> b c h w', h=p2.shape[2], w=p2.shape[3])
        p3 = rearrange(p3_mamba, 'b (h w) c -> b c h w', h=p3.shape[2], w=p3.shape[3])
        p4 = rearrange(p4_mamba, 'b (h w) c -> b c h w', h=p4.shape[2], w=p4.shape[3])
        p5 = rearrange(p5_mamba, 'b (h w) c -> b c h w', h=p5.shape[2], w=p5.shape[3])
        p6 = rearrange(p6_mamba, 'b (h w) c -> b c h w', h=p6.shape[2], w=p6.shape[3])

        return {
            'p2': p2,
            'p3': p3,
            'p4': p4,
            'p5': p5,
            'p6': p6
        }


