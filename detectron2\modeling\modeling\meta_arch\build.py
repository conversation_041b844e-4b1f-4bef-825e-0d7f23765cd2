# Copyright (c) Facebook, Inc. and its affiliates.
import torch
import torch.nn as nn

from detectron2.utils.logger import _log_api_usage
from detectron2.utils.registry import Registry
from .cl_pinn import PhysicsInformedCL, AdaptivePhysicsRegularizer

META_ARCH_REGISTRY = Registry("META_ARCH")  # noqa F401 isort:skip
META_ARCH_REGISTRY.__doc__ = """
Registry for meta-architectures, i.e. the whole model.

The registered object will be called with `obj(cfg)`
and expected to return a `nn.Module` object.
"""


def build_model(cfg):
    """
    Build the whole model architecture, defined by ``cfg.MODEL.META_ARCHITECTURE``.
    Note that it does not load any weights from ``cfg``.
    """
    meta_arch = cfg.MODEL.META_ARCHITECTURE
    model = META_ARCH_REGISTRY.get(meta_arch)(cfg)
    model.to(torch.device(cfg.MODEL.DEVICE))
    _log_api_usage("modeling.meta_arch." + meta_arch)
    return model

def build_physics_cl(cfg):
    """
    构建物理信息引导的增量学习模块
    
    Args:
        cfg (CfgNode): 配置节点
        
    Returns:
        physics_cl (nn.Module): 物理信息引导的增量学习模块
    """
    feature_channels = 256  # 特征通道数，根据backbone输出调整
    memory_size = cfg.MODEL.PHYSICS_CL.MEMORY_SIZE
    device = cfg.MODEL.DEVICE
    initial_a = cfg.MODEL.PHYSICS_CL.INITIAL_A
    max_a = cfg.MODEL.PHYSICS_CL.MAX_A
    diff_coeff = cfg.MODEL.PHYSICS_CL.DIFF_COEFF
    
    return PhysicsInformedCL(
        feature_channels=feature_channels,
        memory_size=memory_size,
        device=device,
        initial_a=initial_a,
        max_a=max_a,
        diff_coeff=diff_coeff
    )
