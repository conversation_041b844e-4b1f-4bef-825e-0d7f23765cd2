import os
import cv2
import torch
from detectron2.engine import DefaultPredictor
from detectron2.config import get_cfg
from detectron2.utils.visualizer import Visualizer
from detectron2.data import MetadataCatalog

# 加载配置并设置
def setup_cfg():
    cfg = get_cfg()
    # 加载 COCO 的配置文件，确保是你自己训练的配置文件
    cfg.merge_from_file("/root/detectron2/configs/COCO-InstanceSegmentation/mask_rcnn_R_50_FPN_3x.yaml")
    
    # 加载训练好的模型权重
    cfg.MODEL.WEIGHTS = "/root/detectron2/tools/output/model_final.pth"
    
    # 设置测试时的阈值，只有置信度大于0.5的检测结果才会被显示
    cfg.MODEL.ROI_HEADS.SCORE_THRESH_TEST = 0.5
    
    # 设置测试数据集名称
    cfg.DATASETS.TEST = ('coco_my_val',)
    
    # 选择使用GPU进行测试（如果有）
    cfg.MODEL.DEVICE = "cuda" if torch.cuda.is_available() else "cpu"
    
    return cfg

# 创建预测器
def create_predictor(cfg):
    return DefaultPredictor(cfg)

# 进行预测
def predict_image(predictor, img_path):
    # 读取图片
    img = cv2.imread(img_path)
    
    # 使用预测器进行预测
    outputs = predictor(img)
    
    # 可视化预测结果
    v = Visualizer(img[:, :, ::-1], MetadataCatalog.get("coco_my_val"), scale=1.2)
    v = v.draw_instance_predictions(outputs["instances"].to("cpu").get_fields()["pred_classes"])
    
    # 显示预测结果
    result_img = v.get_image()[:, :, ::-1]
    cv2.imshow("Predicted Image", result_img)
    cv2.waitKey(0)
    cv2.destroyAllWindows()

# 主程序，遍历图片进行预测
def main():
    # 设置图片目录路径
    image_dir = "path/to/your/images"  # 替换为你自己的图片目录路径
    
    # 设置配置并创建预测器
    cfg = setup_cfg()
    predictor = create_predictor(cfg)
    
    # 遍历目录下的所有图片文件进行预测
    for img_name in os.listdir(image_dir):
        img_path = os.path.join(image_dir, img_name)
        if img_name.endswith(".jpg") or img_name.endswith(".png"):
            print(f"Predicting: {img_path}")
            predict_image(predictor, img_path)

if __name__ == "__main__":
    main()
