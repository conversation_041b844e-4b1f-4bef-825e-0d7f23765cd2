# 基本模型设置
MODEL:
  META_ARCHITECTURE: "GeneralizedRCNN"
  WEIGHTS: "/root/detectron2/tools/output/model_final.pth"  # 训练好的模型权重文件路径
  DEVICE: "cuda"  # 使用 GPU，如果有 CUDA 环境
  BACKBONE:
    NAME: "build_resnet50_fpn_backbone"
    RESNETS:
      DEPTH: 50
      STRIDE_IN_1X1: False
      OUT_FEATURES: ["res5"]
  ROI_HEADS:
    NAME: "StandardROIHeads"
    NUM_CLASSES: 2  # 类别数，背景+tree（如目标检测中只有一个物体：tree）
    IN_FEATURES: ["res5"]
    SCORE_THRESH_TEST: 0.05  # 测试时的阈值
    NMS_THRESH_TEST: 0.5  # 非极大值抑制阈值
    BATCH_SIZE_PER_IMAGE: 256
    TEST_SCORE_THRESH: 0.05

# 数据集设置
DATASETS:
  TRAIN: ("coco_my_train",)
  TEST: ("coco_my_val",)
  # 训练集和测试集名称

# 输入设置
INPUT:
  MIN_SIZE_TRAIN: (640, 640)  # 最小训练图像尺寸
  MAX_SIZE_TRAIN: 640  # 最大训练图像尺寸
  MIN_SIZE_TEST: 640  # 最小测试图像尺寸
  MAX_SIZE_TEST: 640  # 最大测试图像尺寸
  CROP:
    ENABLED: True  # 是否启用裁剪

# 训练相关设置
SOLVER:
  IMS_PER_BATCH: 4  # batch size
  MAX_ITER: 30000  # 最大训练迭代次数
  BASE_LR: 0.002  # 初始学习率
  WEIGHT_DECAY: 0.0001  # 权重衰减
  MOMENTUM: 0.9  # 动量
  GAMMA: 0.1  # 学习率衰减系数
  STEPS: (21000, 25000)  # 学习率衰减的迭代次数
  WARMUP_METHOD: "linear"  # 热身阶段方法
  WARMUP_ITERS: 1000  # 热身阶段迭代次数
  WARMUP_FACTOR: 0.001  # 热身阶段学习率因子

# 测试相关设置
TEST:
  EVAL_PERIOD: 1000  # 每 1000 次迭代评估一次
  DETECTIONS_PER_IMAGE: 100  # 每张图片最多保存100个检测结果

# 输出设置
OUTPUT_DIR: "./result"  # 结果保存的目录
