import torch
import torch.nn as nn
import torch.nn.functional as F
import matplotlib.pyplot as plt
from PIL import Image
import torchvision.transforms as transforms
import numpy as np
import os

class AdaptiveGaussianFilter(nn.Module):
    def __init__(self, in_channels, kernel_size=3, min_sigma=0.3, max_sigma=1.0):
        super(AdaptiveGaussianFilter, self).__init__()
        self.kernel_size = kernel_size
        self.min_sigma = min_sigma
        self.max_sigma = max_sigma
        self.in_channels = in_channels

    def _create_gaussian_kernel(self, sigma):
        # 创建一个标准的高斯核
        x = torch.arange(-self.kernel_size // 2 + 1., self.kernel_size // 2 + 1.)
        y = torch.arange(-self.kernel_size // 2 + 1., self.kernel_size // 2 + 1.)
        xx, yy = torch.meshgrid(x, y, indexing='ij')  # 添加indexing参数
        kernel = torch.exp(-(xx ** 2 + yy ** 2) / (2. * sigma ** 2))
        kernel = kernel / kernel.sum()
        return kernel.unsqueeze(0).unsqueeze(0)

    def forward(self, x):
        # 为每个通道单独处理
        output = torch.zeros_like(x)
        for c in range(self.in_channels):
            # 计算当前通道的局部方差
            channel_data = x[:, c:c+1]
            mean = F.avg_pool2d(channel_data, self.kernel_size, stride=1, padding=self.kernel_size//2)
            squared_mean = F.avg_pool2d(channel_data ** 2, self.kernel_size, stride=1, padding=self.kernel_size//2)
            variance = squared_mean - mean ** 2
            
            # 根据方差动态计算sigma
            sigma = torch.sqrt(torch.clamp(variance, min=0) + 1e-6)
            sigma = torch.clamp(sigma, min=self.min_sigma, max=self.max_sigma)
            
            # 使用平均sigma值创建高斯核
            avg_sigma = sigma.mean()
            kernel = self._create_gaussian_kernel(avg_sigma.item())
            
            # 应用高斯滤波
            output[:, c:c+1] = F.conv2d(channel_data, kernel.to(x.device), padding=self.kernel_size//2)
        
        return output

def process_image(image_path, save_dir=None):
    # 加载图像并调整大小到640x640
    image = Image.open(image_path).convert('RGB')
    image = image.resize((640, 640), Image.Resampling.LANCZOS)
    
    transform = transforms.Compose([
        transforms.ToTensor(),
    ])
    input_tensor = transform(image).unsqueeze(0)
    
    # 初始化自适应高斯滤波
    gaussian = AdaptiveGaussianFilter(in_channels=3)
    
    # 创建保存目录
    if save_dir is None:
        save_dir = os.path.dirname(image_path)
    os.makedirs(save_dir, exist_ok=True)
    base_name = os.path.splitext(os.path.basename(image_path))[0]
    
    with torch.no_grad():
        # 应用自适应高斯滤波
        gaussian_output = gaussian(input_tensor)
        gaussian_image = torch.clamp(gaussian_output[0], 0, 1)
        gaussian_image = transforms.ToPILImage()(gaussian_image)
        gaussian_path = os.path.join(save_dir, f"{base_name}_gaussian.png")
        gaussian_image.save(gaussian_path)
        
        print(f"Saved denoised image: {gaussian_path}")
        return gaussian_image

if __name__ == "__main__":
    # 示例使用
    image_paths = [
        r"E:\home\data\pix4d\data\one\tile_366_4500_18000.tif",
        r"E:\home\data\pix4d\data\one\tile_367_5400_18000.tif"
    ]
    
    output_dir = os.path.join(os.path.dirname(image_paths[0]), "denoised_images")
    
    for img_path in image_paths:
        try:
            process_image(img_path, output_dir)
        except Exception as e:
            print(f"Error processing {img_path}: {str(e)}")
