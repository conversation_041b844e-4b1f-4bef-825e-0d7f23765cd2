import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import numpy as np
from .CL import MemoryBuffer


class PhysicsGuidedAttention(nn.Module):
    """物理引导的注意力机制，根据物理先验信息增强特征表示"""
    
    def __init__(self, channels, diff_coeff=0.1, reduction=8):
        super().__init__()
        self.channels = channels
        self.diff_coeff = diff_coeff  # 扩散系数
        
        # 通道注意力层
        self.channel_attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels, channels // reduction, kernel_size=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // reduction, channels, kernel_size=1),
            nn.Sigmoid()
        )
        
        # 物理约束层 - 拉普拉斯算子
        self.laplacian_kernel = torch.tensor([
            [0.0, 1.0, 0.0],
            [1.0, -4.0, 1.0],
            [0.0, 1.0, 0.0]
        ], dtype=torch.float32).view(1, 1, 3, 3)
        
        # 可学习的权重，平衡物理先验和数据驱动特征
        self.alpha = nn.Parameter(torch.tensor(0.5))
    
    def forward(self, x):
        """
        前向传播
        
        Args:
            x: 输入特征 [B, C, H, W]
            
        Returns:
            y: 增强特征 [B, C, H, W]
        """
        B, C, H, W = x.shape
        
        # 通道注意力
        channel_weights = self.channel_attention(x)
        
        # 应用物理约束（扩散）- 对每个通道应用拉普拉斯算子
        laplacian_kernel = self.laplacian_kernel.to(x.device)
        physical_features = []
        for i in range(C):
            channel = x[:, i:i+1, :, :]
            # 应用拉普拉斯算子，模拟扩散过程
            physical_feature = F.conv2d(channel, laplacian_kernel, padding=1)
            # 根据扩散方程: du/dt = D * ∇²u
            physical_feature = x[:, i:i+1, :, :] + self.diff_coeff * physical_feature
            physical_features.append(physical_feature)
        
        physical_features = torch.cat(physical_features, dim=1)
        
        # 融合物理引导特征和通道注意力特征
        y = torch.sigmoid(self.alpha) * (x * channel_weights) + (1 - torch.sigmoid(self.alpha)) * physical_features
        
        return y


class PhysicsInformedCL(nn.Module):
    """
    物理信息引导的增量学习模块
    
    结合PINN和增量学习，使用物理先验知识指导特征学习和记忆融合
    """
    
    def __init__(self, feature_channels=256, memory_size=200, device='cpu',
                initial_a=0.01, max_a=1.0, diff_coeff=0.1, enable_cl=False):
        super().__init__()
        self.feature_channels = feature_channels
        self.device = device
        self.a = initial_a  # 控制记忆利用的因子
        self.max_a = max_a
        self.diff_coeff = diff_coeff
        self.enable_cl = enable_cl  # 是否启用持续学习功能
        
        # 特征增强模块 - 只为p3特征级别创建物理引导注意力模块
        self.attention_modules = nn.ModuleDict({
            'p3': PhysicsGuidedAttention(feature_channels, diff_coeff)
        })
        
        # 物理保持投影 - 确保特征满足物理约束
        self.physics_projections = nn.ModuleDict({
            'p3': nn.Conv2d(feature_channels, feature_channels, kernel_size=3, padding=1)
        })
        
        # 内存缓冲区 - 用于存储历史特征
        self.memory_buffer = MemoryBuffer(
            max_size=memory_size,
            device=device
        )
        
        # 边界条件生成器 - 为物理模型提供边界约束
        self.register_buffer('boundary_conditions', self._generate_boundary_conditions())
        
        # 物理损失权重 - 降低权重避免过度约束
        self.physics_weight = nn.Parameter(torch.tensor(0.05))
        
        # 物理与记忆融合比例 - 默认更偏向原始特征
        self.physics_memory_ratio = nn.Parameter(torch.tensor(0.2))
        
        # 移动到指定设备
        self.to(device)
    
    def _generate_boundary_conditions(self):
        """生成用于物理模型的边界条件"""
        # 简化的边界条件，实际应用中可能更复杂
        x = torch.linspace(0, 1, 100)
        boundary_values = torch.sin(2 * math.pi * x) * 0.5 + 0.5  # 范围[0,1]的正弦波
        return boundary_values.unsqueeze(1)  # [100, 1]
    
    def _apply_boundary_conditions(self, feature, level):
        """将边界条件应用到特征上"""
        B, C, H, W = feature.shape
        
        # 边界条件
        bc = self.boundary_conditions.to(feature.device)
        bc_scaled = F.interpolate(bc.unsqueeze(0).unsqueeze(0), 
                                size=(H, W), 
                                mode='bilinear', 
                                align_corners=False).repeat(B, 1, 1, 1)
        
        # 创建边界掩码 - 只在特征图的边缘应用边界条件
        mask = torch.ones((B, 1, H, W), device=feature.device)
        mask[:, :, 0, :] = 0  # 上边界
        mask[:, :, -1, :] = 0  # 下边界
        mask[:, :, :, 0] = 0  # 左边界
        mask[:, :, :, -1] = 0  # 右边界
        
        # 计算边界权重因子 - 根据特征层级调整
        level_factor = 0.1 / (int(level[1]) + 1)  # p2->0.033, p3->0.025, p4->0.02, p5->0.016
        
        # 应用边界条件 - 在边界处融合物理先验
        boundary_feature = feature * mask + bc_scaled * level_factor * (1 - mask)
        
        return boundary_feature
    
    def _compute_physics_loss(self, feature):
        """计算物理一致性损失"""
        # 使用拉普拉斯算子和扩散方程计算物理损失
        B, C, H, W = feature.shape
        laplacian_kernel = torch.tensor([
            [0.0, 1.0, 0.0],
            [1.0, -4.0, 1.0],
            [0.0, 1.0, 0.0]
        ], dtype=torch.float32).view(1, 1, 3, 3).to(feature.device)
        
        # 对每个通道计算拉普拉斯
        physics_loss = 0
        for i in range(C):
            channel = feature[:, i:i+1, :, :]
            laplacian = F.conv2d(channel, laplacian_kernel, padding=1)
            
            # 扩散方程: du/dt = D * ∇²u，在稳态下应该接近0
            residual = self.diff_coeff * laplacian
            physics_loss += torch.mean(residual ** 2)
        
        return physics_loss / C
    
    def set_cl_enabled(self, enabled=True):
        """设置是否启用持续学习功能"""
        self.enable_cl = enabled
        print(f"持续学习功能已{'启用' if enabled else '禁用'}")
    
    def forward(self, features, update_memory=True, use_old_features=True):
        """
        增强输入特征并融合记忆中的特征
        
        Args:
            features: 从主干网络生成的特征字典 {level_name: tensor, ...}
            update_memory: 是否更新内存缓冲区
            use_old_features: 是否使用旧特征进行融合
            
        Returns:
            enhanced_features: 增强后的特征字典
            physics_losses: 物理一致性损失
        """
        # 如果持续学习被禁用，只应用物理增强，不使用/更新记忆
        if not self.enable_cl:
            use_old_features = False
            update_memory = False
            
        # 存储增强后的特征和物理损失
        enhanced_features = {}
        physics_losses = {}
        
        # 处理除了p3之外的所有特征级别 - 直接传递
        for key, feature in features.items():
            if key != 'p3':
                enhanced_features[key] = feature
        
        # 只对p3特征图应用物理增强和记忆融合
        if 'p3' in features and 'p3' in self.attention_modules:
            # 获取原始p3特征
            original_p3 = features['p3']
            
            # 1. 应用物理引导注意力
            physics_enhanced = self.attention_modules['p3'](original_p3)
            
            # 2. 应用边界条件
            physics_enhanced = self._apply_boundary_conditions(physics_enhanced, 'p3')
            
            # 3. 通过物理保持投影
            physics_enhanced = self.physics_projections['p3'](physics_enhanced)
            
            # 4. 计算物理损失
            physics_loss = self._compute_physics_loss(physics_enhanced)
            physics_losses['p3'] = physics_loss * torch.sigmoid(self.physics_weight)
            
            # 5. 处理记忆融合（如果需要）
            memory_enhanced = original_p3.clone()  # 默认使用原始特征
            
            if use_old_features and len(self.memory_buffer.buffer) > 0:
                # 计算要使用的历史特征数量
                num_samples = int(min(100, self.memory_buffer.max_size) * self.a)
                
                if num_samples > 0:
                    # 获取记忆中的特征
                    old_features = self.memory_buffer.sample(
                        num_samples=num_samples
                    )
                    
                    # 融合历史特征和原始特征
                    p3_dict = {'p3': original_p3}
                    fused_p3 = self._fuse_features(p3_dict, old_features)
                    memory_enhanced = fused_p3['p3']
            
            # 6. 最终融合 - 原始特征作为主要部分，物理和记忆特征作为增强部分
            # 使用可学习的融合比例，但确保原始特征权重保持为1.0
            ratio = torch.sigmoid(self.physics_memory_ratio)  # 物理与记忆增强部分的权重
            enhanced_p3 = original_p3 + 0.1 * (ratio * physics_enhanced + (1 - ratio) * memory_enhanced)
            
            # 7. 保存增强后的p3特征
            enhanced_features['p3'] = enhanced_p3
        
        # 8. 更新内存（如果需要）- 只记忆p3特征图
        if update_memory and 'p3' in enhanced_features:
            features_for_memory = {'p3': enhanced_features['p3'].detach()}
            self.memory_buffer.add(features_for_memory)
        
        return enhanced_features, physics_losses
    
    def _fuse_features(self, current_features, old_features_list):
        """融合当前特征和历史特征"""
        fused_features = {}
        
        for key in current_features.keys():
            x_current = current_features[key]
            
            # 收集有效的历史特征
            valid_old_features = []
            for old_feat_dict in old_features_list:
                if key in old_feat_dict:
                    old_feat = old_feat_dict[key].to(x_current.device)
                    
                    # 确保尺寸匹配
                    if old_feat.shape[2:] != x_current.shape[2:]:
                        old_feat = F.interpolate(
                            old_feat,
                            size=x_current.shape[2:],
                            mode='bilinear',
                            align_corners=False
                        )
                    
                    valid_old_features.append(old_feat)
            
            if valid_old_features:
                # 基于物理约束的注意力融合
                B, C, H, W = x_current.shape
                
                # 计算注意力权重 - 考虑物理约束
                attn_weights = []
                
                # 当前特征权重始终为1
                attn_weights.append(1.0)
                
                for old_feat in valid_old_features:
                    # 计算物理一致性分数
                    physics_loss = self._compute_physics_loss(old_feat)
                    # 基于物理损失的权重（损失越小权重越大）
                    physics_weight = torch.exp(-10 * physics_loss)
                    
                    # 计算特征相似度
                    similarity = F.cosine_similarity(
                        x_current.view(B, -1),
                        old_feat.view(B, -1),
                        dim=1
                    ).mean()
                    
                    # 综合考虑物理一致性和特征相似度
                    weight = physics_weight * max(0, similarity.item()) + 1e-6
                    attn_weights.append(weight.item())
                
                # 归一化权重
                sum_weights = sum(attn_weights)
                norm_weights = [w / sum_weights for w in attn_weights]
                
                # 加权融合
                fused = x_current * norm_weights[0]
                for i, old_feat in enumerate(valid_old_features):
                    fused = fused + old_feat * norm_weights[i+1]
                
                # 应用物理保持投影确保物理一致性
                if key in self.physics_projections:
                    fused = self.physics_projections[key](fused)
                
                fused_features[key] = fused
            else:
                fused_features[key] = x_current
        
        return fused_features
    
    def update_factor_a(self, a):
        """更新控制记忆使用的因子"""
        self.a = min(max(a, 0.0), self.max_a)
    
    def get_physics_parameters(self):
        """获取物理模型参数，用于外部优化"""
        return {
            'diff_coeff': self.diff_coeff,
            'physics_weight': torch.sigmoid(self.physics_weight).item(),
            'alpha_values': {k: torch.sigmoid(v.alpha).item() for k, v in self.attention_modules.items()}
        }


class AdaptivePhysicsRegularizer(nn.Module):
    """
    自适应物理正则化器
    
    根据任务难度和数据分布动态调整物理约束的强度
    """
    
    def __init__(self, feature_channels=256, pde_type='diffusion'):
        super().__init__()
        self.feature_channels = feature_channels
        self.pde_type = pde_type  # 'diffusion', 'wave', 'reaction-diffusion'
        
        # 物理系数预测网络 - 根据特征预测最佳的物理系数
        self.physics_predictor = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Flatten(),
            nn.Linear(feature_channels, 64),
            nn.ReLU(inplace=True),
            nn.Linear(64, 3)  # 预测[扩散系数, 反应系数, 正则化权重]
        )
        
        # 拉普拉斯和梯度算子
        self.laplacian_kernel = torch.tensor([
            [0.0, 1.0, 0.0],
            [1.0, -4.0, 1.0],
            [0.0, 1.0, 0.0]
        ], dtype=torch.float32).view(1, 1, 3, 3)
        
        self.grad_x_kernel = torch.tensor([
            [-1.0, 0.0, 1.0],
            [-2.0, 0.0, 2.0],
            [-1.0, 0.0, 1.0]
        ], dtype=torch.float32).view(1, 1, 3, 3) / 8.0
        
        self.grad_y_kernel = torch.tensor([
            [-1.0, -2.0, -1.0],
            [0.0, 0.0, 0.0],
            [1.0, 2.0, 1.0]
        ], dtype=torch.float32).view(1, 1, 3, 3) / 8.0
    
    def forward(self, features):
        """
        前向传播，计算物理损失
        
        Args:
            features: 特征字典
            
        Returns:
            physics_losses: 物理损失字典
            physics_params: 物理参数字典
        """
        physics_losses = {}
        physics_params = {}
        
        for key, feature in features.items():
            B, C, H, W = feature.shape
            
            # 预测物理参数
            params = self.physics_predictor(feature)
            diff_coeff = torch.sigmoid(params[:, 0]) * 0.5  # 扩散系数 [0, 0.5]
            react_coeff = torch.sigmoid(params[:, 1]) * 2.0 - 1.0  # 反应系数 [-1, 1]
            reg_weight = torch.sigmoid(params[:, 2]) * 0.2  # 正则化权重 [0, 0.2]
            
            # 记录参数
            physics_params[key] = {
                'diff_coeff': diff_coeff.mean().item(),
                'react_coeff': react_coeff.mean().item(),
                'reg_weight': reg_weight.mean().item()
            }
            
            # 将卷积核移到正确设备
            laplacian_kernel = self.laplacian_kernel.to(feature.device)
            grad_x_kernel = self.grad_x_kernel.to(feature.device)
            grad_y_kernel = self.grad_y_kernel.to(feature.device)
            
            # 计算物理损失
            physics_loss = 0
            for i in range(C):
                channel = feature[:, i:i+1, :, :]
                
                # 拉普拉斯项 (∇²u)
                laplacian = F.conv2d(channel, laplacian_kernel, padding=1)
                
                if self.pde_type == 'diffusion':
                    # 扩散方程: du/dt = D∇²u
                    residual = diff_coeff.view(B, 1, 1, 1) * laplacian
                
                elif self.pde_type == 'reaction-diffusion':
                    # 反应-扩散方程: du/dt = D∇²u + R*u
                    residual = diff_coeff.view(B, 1, 1, 1) * laplacian + react_coeff.view(B, 1, 1, 1) * channel
                
                elif self.pde_type == 'wave':
                    # 波动方程的简化形式: ∇²u + u = 0
                    residual = laplacian + channel
                
                else:
                    # 默认使用扩散方程
                    residual = diff_coeff.view(B, 1, 1, 1) * laplacian
                
                # 计算物理残差的MSE
                physics_loss += torch.mean(residual ** 2)
            
            # 平均所有通道的损失，乘以正则化权重
            physics_losses[key] = (physics_loss / C) * reg_weight.mean()
        
        return physics_losses, physics_params 