import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from .transform_processor import LaplacianTransform


class EdgeEnhancementModule(nn.Module):
    """边缘增强模块，使用拉普拉斯变换提取图像边缘特征"""
    
    def __init__(self, channels, k_size=3):
        super().__init__()
        self.channels = channels
        self.k_size = k_size
        
        # 拉普拉斯算子核心
        # 标准拉普拉斯卷积核: [[0, 1, 0], [1, -4, 1], [0, 1, 0]]
        laplacian_kernel = torch.zeros(1, 1, k_size, k_size)
        if k_size == 3:
            laplacian_kernel[0, 0, 1, 0] = 1.0
            laplacian_kernel[0, 0, 0, 1] = 1.0
            laplacian_kernel[0, 0, 1, 2] = 1.0
            laplacian_kernel[0, 0, 2, 1] = 1.0
            laplacian_kernel[0, 0, 1, 1] = -4.0
        else:
            # 对于不同尺寸的卷积核，需要调整权重
            center = k_size // 2
            for i in range(k_size):
                for j in range(k_size):
                    if (i == center and j != center) or (i != center and j == center):
                        laplacian_kernel[0, 0, i, j] = 1.0
            laplacian_kernel[0, 0, center, center] = -1.0 * (k_size - 1)
        
        # 注册为不需要梯度的参数
        self.register_buffer('laplacian_kernel', laplacian_kernel)
        
        # 可学习的通道混合层
        self.edge_mixer = nn.Sequential(
            nn.Conv2d(channels, channels, kernel_size=1),
            nn.BatchNorm2d(channels),
            nn.SiLU()
        )
        
        # 特征增强层
        self.enhance = nn.Sequential(
            nn.Conv2d(channels * 2, channels, kernel_size=1),
            nn.BatchNorm2d(channels),
            nn.SiLU()
        )
    
    def forward(self, x):
        """
        前向传播
        
        Args:
            x: 输入特征 [B, C, H, W]
            
        Returns:
            enhanced_features: 边缘增强特征 [B, C, H, W]
        """
        B, C, H, W = x.shape
        
        # 应用拉普拉斯算子到每个通道
        edge_features = []
        for i in range(C):
            channel = x[:, i:i+1, :, :]
            edge = F.conv2d(channel, self.laplacian_kernel, padding=self.k_size//2)
            edge_features.append(edge)
        
        # 叠加所有通道的边缘特征
        edge_features = torch.cat(edge_features, dim=1)
        
        # 边缘特征混合
        edge_features = self.edge_mixer(edge_features)
        
        # 特征融合
        enhanced_features = torch.cat([x, edge_features], dim=1)
        enhanced_features = self.enhance(enhanced_features)
        
        return enhanced_features


class LaplacianProcessor(nn.Module):
    """拉普拉斯变换处理器，用于增强特征中的边缘信息"""
    
    def __init__(self, channels, depth=2, k_size=3):
        super().__init__()
        self.channels = channels
        self.depth = depth
        
        # 边缘增强模块
        self.edge_modules = nn.ModuleList([
            EdgeEnhancementModule(channels, k_size) for _ in range(depth)
        ])
        
        # 残差连接
        self.fusion = nn.Sequential(
            nn.Conv2d(channels, channels, kernel_size=1),
            nn.BatchNorm2d(channels),
            nn.SiLU()
        )
    
    def forward(self, x):
        """
        前向传播
        
        Args:
            x: 输入特征 [B, C, H, W]
            
        Returns:
            out: 增强后的特征 [B, C, H, W]
        """
        identity = x
        
        # 依次应用边缘增强模块
        for module in self.edge_modules:
            x = module(x)
        
        # 最终融合
        out = self.fusion(x) + identity
        
        return out 

def _create_laplacian_kernel(self, scale):
    # 只返回基础核，不进行通道复制
    if scale == 3:
        laplacian_kernel = torch.tensor([
            [1, 2, 1],
            [2, -12, 2],
            [1, 2, 1]
        ], dtype=torch.float32).unsqueeze(0).unsqueeze(0)  # 移除 .repeat(x.shape[1], 1, 1, 1)
    elif scale == 5:
        laplacian_kernel = torch.tensor([
            [-2, -4, -4, -4, -2],
            [-4, 0, 8, 0, -4],
            [-4, 8, 24, 8, -4],
            [-4, 0, 8, 0, -4],
            [-2, -4, -4, -4, -2]
        ], dtype=torch.float32).unsqueeze(0).unsqueeze(0)  # 移除 .repeat(x.shape[1], 1, 1, 1)
    elif scale == 7:
        # ... 类似修改其他情况
    else:
        raise ValueError("Unsupported scale")
    return laplacian_kernel  # 只返回基础核

def forward(self, x):
    # ... 现有代码 ...
    
    laplacian_kernels = []
    for scale in self.scales:
        base_kernel = self._create_laplacian_kernel(scale)
        # 在这里动态重复通道维度
        repeated_kernel = base_kernel.repeat(x.shape[1], 1, 1, 1)
        laplacian_kernels.append(repeated_kernel)
    
    # 使用自适应的高斯滤波器
    if self.use_adaptive_filtering:
        adaptive_filter = AdaptiveGaussianFilter(in_channels=x.shape[1])

    # 初始化输出张量
    output = torch.zeros_like(x)

    for i, laplacian_kernel in enumerate(laplacian_kernels):
        # 对实部和虚部分别应用拉普拉斯算子
        real_output = F.conv2d(real_part, laplacian_kernel, groups=real_part.size(1), padding=1)
        imag_output = F.conv2d(imag_part, laplacian_kernel, groups=imag_part.size(1), padding=1)

        # 合并为通道维度扩展的张量
        combined_features = torch.cat([real_output, imag_output], dim=1)

        # 应用自适应的高斯滤波器
        if self.use_adaptive_filtering:
            combined_features = adaptive_filter(combined_features)

        # 通过1x1卷积融合通道
        fusion_kernel = self._create_fusion_kernel(i)
        output += F.conv2d(combined_features, fusion_kernel, padding=0, groups=x.shape[1])

    return output 

class LaplacianTransform(nn.Module):
    def __init__(self, scales, use_adaptive_filtering=True):
        super(LaplacianTransform, self).__init__()
        self.scales = scales
        self.use_adaptive_filtering = use_adaptive_filtering
    
    def _create_laplacian_kernel(self, scale):
        # 只返回基础核，不进行通道复制
        if scale == 3:
            laplacian_kernel = torch.tensor([
                [1, 2, 1],
                [2, -12, 2],
                [1, 2, 1]
            ], dtype=torch.float32).unsqueeze(0).unsqueeze(0)  # 移除 .repeat(x.shape[1], 1, 1, 1)
        elif scale == 5:
            laplacian_kernel = torch.tensor([
                [-2, -4, -4, -4, -2],
                [-4, 0, 8, 0, -4],
                [-4, 8, 24, 8, -4],
                [-4, 0, 8, 0, -4],
                [-2, -4, -4, -4, -2]
            ], dtype=torch.float32).unsqueeze(0).unsqueeze(0)  # 移除 .repeat(x.shape[1], 1, 1, 1)
        elif scale == 7:
            # ... 类似修改其他情况
        else:
            raise ValueError("Unsupported scale")
        return laplacian_kernel  # 只返回基础核
        
    def _create_fusion_kernel(self, index):
        # 创建1x1卷积核用于融合不同尺度的特征
        return torch.tensor([0] * index + [1] + [0] * (len(self.scales) - index - 1), dtype=torch.float32).unsqueeze(1).unsqueeze(1)
    
    def forward(self, x):
        # 确保输入是4D张量 (batch_size, channels, height, width)
        if x.dim() == 3:  # 如果输入是3D，增加一个 batch_size 维度
            x = x.unsqueeze(0)

        if x.dtype == torch.complex64:
            # 提取实部和虚部
            real_part = torch.real(x)
            imag_part = torch.imag(x)
        else:
            # 如果是实数张量，将其转为复数张量的输入形式
            real_part = x
            imag_part = torch.zeros_like(x)

        # 对实部和虚部分别应用不同尺度的拉普拉斯算子
        laplacian_kernels = []
        for scale in self.scales:
            # 获取基础卷积核
            base_kernel = self._create_laplacian_kernel(scale)
            # 在这里动态复制到所需通道数
            full_kernel = base_kernel.repeat(x.shape[1], 1, 1, 1)
            laplacian_kernels.append(full_kernel)
            
        # 使用自适应的高斯滤波器
        if self.use_adaptive_filtering:
            adaptive_filter = AdaptiveGaussianFilter(in_channels=x.shape[1])

        # 初始化输出张量
        output = torch.zeros_like(x)

        for i, laplacian_kernel in enumerate(laplacian_kernels):
            # 对实部和虚部分别应用拉普拉斯算子
            real_output = F.conv2d(real_part, laplacian_kernel, groups=real_part.size(1), padding=1)
            imag_output = F.conv2d(imag_part, laplacian_kernel, groups=imag_part.size(1), padding=1)

            # 合并为通道维度扩展的张量
            combined_features = torch.cat([real_output, imag_output], dim=1)

            # 应用自适应的高斯滤波器
            if self.use_adaptive_filtering:
                combined_features = adaptive_filter(combined_features)

            # 通过1x1卷积融合通道
            fusion_kernel = self._create_fusion_kernel(i)
            output += F.conv2d(combined_features, fusion_kernel, padding=0, groups=x.shape[1])

        return output 