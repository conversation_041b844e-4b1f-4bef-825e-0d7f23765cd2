import logging
import os
from collections import OrderedDict

import numpy as np
from PIL import ImageFile, Image
import torch
import detectron2.utils.comm as comm
from detectron2.checkpoint import DetectionCheckpointer
from detectron2.config import get_cfg
from detectron2.data import MetadataCatalog
from detectron2.engine import DefaultTrainer, default_argument_parser, default_setup, hooks, launch
from detectron2.evaluation import (
    CityscapesInstanceEvaluator,
    CityscapesSemSegEvaluator,
    COCOEvaluator,
    COCOPanopticEvaluator,
    DatasetEvaluators,
    LVISEvaluator,
    PascalVOCDetectionEvaluator,
    SemSegEvaluator,
    verify_results,
)
from detectron2.modeling import GeneralizedRCNNWithTTA

from detectron2.data.datasets.builtin import *
from detectron2.utils.events import get_event_storage

# 允许加载截断图像
ImageFile.LOAD_TRUNCATED_IMAGES = True

# 导入 matplotlib.pyplot
import matplotlib.pyplot as plt

# 自定义读取图像的函数，加入错误处理
def read_image_with_error_handling(path, format=None):
    try:
        image = Image.open(path)
        image.load()
        return image
    except (IOError, OSError) as e:
        logging.error(f"Error loading image {path}: {e}")
        return None  # 你可以选择跳过该图像


def setup(args):
    """
    Create configs and perform basic setups.
    """
    cfg = get_cfg()
    args.config_file = "/root/detectron2/configs/COCO-InstanceSegmentation/mask_rcnn_R_50_FPN_3x.yaml"
    cfg.merge_from_file(args.config_file)  # 从config file 覆盖配置
    cfg.merge_from_list(args.opts)  # 从CLI参数 覆盖配置

    # 更改配置参数
    cfg.DATASETS.TRAIN = ('coco_my_train',)  # 训练数据集名称
    cfg.DATASETS.TEST = ('coco_my_val',)
    cfg.DATALOADER.NUM_WORKERS = 1  # 单线程

    cfg.INPUT.CROP.ENABLED = True
    cfg.INPUT.MAX_SIZE_TRAIN = 640  # 训练图片输入的最大尺寸
    cfg.INPUT.MAX_SIZE_TEST = 640  # 测试数据输入的最大尺寸
    cfg.INPUT.MIN_SIZE_TRAIN = (640, 640)  # 训练图片输入的最小尺寸，可以设定为多尺度训练
    cfg.INPUT.MIN_SIZE_TEST = 640
    # cfg.INPUT.MIN_SIZE_TRAIN_SAMPLING，其存在两种配置，分别为 choice 与 range ：
    # range 让图像的短边从 512-768随机选择
    # choice ： 把输入图像转化为指定的，有限的几种图片大小进行训练，即短边只能为 512或者768
    cfg.INPUT.MIN_SIZE_TRAIN_SAMPLING = 'range'

    cfg.MODEL.RETINANET.NUM_CLASSES = 2  # 类别数+1（因为有background）
    # 设置模型架构为 GeneralizedRCNN
    cfg.MODEL.META_ARCHITECTURE = "GeneralizedRCNN"
    # 设置权重为空字符串表示从头开始训练
    cfg.MODEL.WEIGHTS = "/root/autodl-tmp/output4/model_0003487.pth"  # 从头开始训练

    cfg.SOLVER.IMS_PER_BATCH = 2  # batch_size=2; iters_in_one_epoch = dataset_imgs/batch_size

    # 根据训练数据总数目以及batch_size，计算出每个epoch需要的迭代次数
    # 9000为你的训练数据的总数目，可自定义
    ITERS_IN_ONE_EPOCH = int(220 / cfg.SOLVER.IMS_PER_BATCH)

    # 指定最大迭代次数
    cfg.SOLVER.MAX_ITER = (ITERS_IN_ONE_EPOCH * 50) - 1  # 50 epochs，
    # 初始学习率
    cfg.SOLVER.BASE_LR = 0.002
    # 优化器动能
    cfg.SOLVER.MOMENTUM = 0.9
    # 权重衰减
    cfg.SOLVER.WEIGHT_DECAY = 0.0001
    cfg.SOLVER.WEIGHT_DECAY_NORM = 0.0
    # 学习率衰减倍数
    cfg.SOLVER.GAMMA = 0.1
    # 迭代到指定次数，学习率进行衰减
    cfg.SOLVER.STEPS = (700,)
    # 在训练之前，会做一个热身运动，学习率慢慢增加初始学习率
    cfg.SOLVER.WARMUP_FACTOR = 1.0 / 1000
    # 热身迭代次数
    cfg.SOLVER.WARMUP_ITERS = 300

    cfg.SOLVER.WARMUP_METHOD = "linear"
    # 保存模型文件的命名数据减1
    cfg.SOLVER.CHECKPOINT_PERIOD = ITERS_IN_ONE_EPOCH - 1

    # 迭代到指定次数，进行一次评估
    cfg.TEST.EVAL_PERIOD = ITERS_IN_ONE_EPOCH

    # 打印当前设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logging.info(f"Using device: {device}")

    cfg.freeze()
    default_setup(cfg, args)
    return cfg


class Trainer(DefaultTrainer):
    @classmethod
    def build_evaluator(cls, cfg, dataset_name, output_folder=None):
        if output_folder is None:
            output_folder = os.path.join(cfg.OUTPUT_DIR, "inference")
        return COCOEvaluator(dataset_name, cfg, distributed=False, output_dir=output_folder)

    @classmethod
    def test_with_TTA(cls, cfg, model):
        logger = logging.getLogger("detectron2.trainer")
        # In the end of training, run an evaluation with TTA
        # Only support some R-CNN models.
        logger.info("Running inference with test-time augmentation ...")
        model = GeneralizedRCNNWithTTA(cfg, model)
        evaluators = [
            cls.build_evaluator(
                cfg, name, output_folder=os.path.join(cfg.OUTPUT_DIR, "inference_TTA")
            )
            for name in cfg.DATASETS.TEST
        ]
        res = cls.test(cfg, model, evaluators)
        res = OrderedDict({k + "_TTA": v for k, v in res.items()})
        return res

    def after_train(self):
        super().after_train()
        self.plot_loss_curve()
        self.plot_ap_curves()

    def plot_loss_curve(self):
        storage = get_event_storage()
        if "loss" not in storage._history:
            self._logger.warning("No loss data available in EventStorage!")
            return

        loss_values = storage._history["loss"]
        iterations = list(range(len(loss_values)))

        plt.figure(figsize=(12, 8))
        plt.plot(iterations, loss_values, label="Loss")
        plt.xlabel('Iterations')
        plt.ylabel('Loss')
        plt.title('Training Loss Curve')
        plt.legend(loc='upper right')
        plt.grid(True)
        plt.tight_layout()
        save_path = os.path.join(self.cfg.OUTPUT_DIR, 'loss_curve.png')
        plt.savefig(save_path)
        plt.close()
        print(f"Saved loss curve to {save_path}")

    def plot_ap_curves(self):
        if "bbox" in self._results:
            bbox_results = self._results["bbox"]
            if "precision_data" in bbox_results:
                precision_data = bbox_results["precision_data"]
                class_names = self._metadata.get("thing_classes")
                self._plot_ap_curve(class_names, precision_data, "bbox")

        if "segm" in self._results:
            segm_results = self._results["segm"]
            if "precision_data" in segm_results:
                precision_data = segm_results["precision_data"]
                class_names = self._metadata.get("thing_classes")
                self._plot_ap_curve(class_names, precision_data, "segm")

    def _plot_ap_curve(self, class_names, precision_data, iou_type):
        """
        Plot AP curve for a given iou_type.

        Args:
            class_names (list[str]): list of class names
            precision_data (list[list[float]]): list of precision values for each class and each IoU threshold
            iou_type (str): type of evaluation (bbox, segm, keypoints)
        """
        iou_thresholds = np.linspace(0.5, 0.95, 10)  # IoU thresholds from 0.5 to 0.95 with 0.05 step

        plt.figure(figsize=(12, 8))
        for idx, name in enumerate(class_names):
            precision = precision_data[idx]
            if len(precision) == 0:
                self._logger.warning(f"No precision data for class {name} in {iou_type}")
                continue
            plt.plot(iou_thresholds, precision, label=name)

        plt.xlabel('IoU Threshold')
        plt.ylabel('Precision')
        plt.title(f'Precision vs IoU for {iou_type}')
        plt.legend(loc='lower left', bbox_to_anchor=(1, 0))
        plt.grid(True)
        plt.tight_layout()
        save_path = os.path.join(self.cfg.OUTPUT_DIR, f'ap_curve_{iou_type}.png')
        plt.savefig(save_path)
        plt.close()
        self._logger.info(f"Saved AP curve to {save_path}")


def main(args):
    cfg = setup(args)

    print(cfg)

    # 注册数据集
    register_dataset()

    if args.eval_only:
        model = Trainer.build_model(cfg)
        DetectionCheckpointer(model, save_dir=cfg.OUTPUT_DIR).resume_or_load(
            cfg.MODEL.WEIGHTS, resume=args.resume
        )
        res = Trainer.test(cfg, model)
        if cfg.TEST.AUG.ENABLED:
            res.update(Trainer.test_with_TTA(cfg, model))
        if comm.is_main_process():
            verify_results(cfg, res)
        return res

    trainer = Trainer(cfg)
    trainer.resume_or_load(resume=args.resume)
    if cfg.TEST.AUG.ENABLED:
        trainer.register_hooks(
            [hooks.EvalHook(0, lambda: trainer.test_with_TTA(cfg, trainer.model))]  # eval hook for TTA
        )
    return trainer.train()


if __name__ == "__main__":
    args = default_argument_parser().parse_args()
    main(args)
