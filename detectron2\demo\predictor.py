# predictor.py
# Copyright (c) Facebook, Inc. and its affiliates.
import atexit
import bisect
import multiprocessing as mp
from collections import deque
import cv2
import torch
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
from unicodedata import normalize

from detectron2.data import MetadataCatalog
from detectron2.engine.defaults import DefaultPredictor
from detectron2.utils.video_visualizer import VideoVisualizer
from detectron2.utils.visualizer import ColorMode, Visualizer


class VisualizationDemo(object):
    def __init__(self, cfg, instance_mode=ColorMode.IMAGE, parallel=False):
        """
        Args:
            cfg (CfgNode):
            instance_mode (ColorMode):
            parallel (bool): whether to run the model in different processes from visualization.
                Useful since the visualization logic can be slow.
        """
        self.metadata = MetadataCatalog.get(
            cfg.DATASETS.TEST[0] if len(cfg.DATASETS.TEST) else "__unused"
        )
        self.cpu_device = torch.device("cpu")
        self.instance_mode = instance_mode

        self.parallel = parallel
        if parallel:
            num_gpu = torch.cuda.device_count()
            self.predictor = AsyncPredictor(cfg, num_gpus=num_gpu)
        else:
            self.predictor = DefaultPredictor(cfg)

    def run_on_image(self, image, start_instance_id=1):
        """
        Args:
            image (np.ndarray): an image of shape (H, W, C) (in BGR order).
                This is the format used by OpenCV.
            start_instance_id (int): 实例编号的起始值，用于跨图片连续编号

        Returns:
            predictions (dict): the output of the model.
            vis_output (VisImage): the visualized image output with instance segmentation.
            indices_outputs (dict): 每个植被指数的可视化图像
            indices_values (dict): 每个实例的所有植被指数值
        """
        predictions = self.predictor(image)
        # Convert image from OpenCV BGR format to Matplotlib RGB format.
        image_rgb = image[:, :, ::-1]
        visualizer = Visualizer(image_rgb, self.metadata, instance_mode=self.instance_mode)

        if "panoptic_seg" in predictions:
            panoptic_seg, segments_info = predictions["panoptic_seg"]
            vis_output = visualizer.draw_panoptic_seg_predictions(
                panoptic_seg.to(self.cpu_device), segments_info
            )
            return predictions, vis_output, None, None
        else:
            if "sem_seg" in predictions:
                vis_output = visualizer.draw_sem_seg(
                    predictions["sem_seg"].argmax(dim=0).to(self.cpu_device)
                )
            if "instances" in predictions:
                instances = predictions["instances"].to(self.cpu_device)
                # 创建实例分割可视化
                vis_output = visualizer.draw_instance_predictions(instances)

                # 计算所有植被指数
                indices_outputs, indices_values = self.calculate_vegetation_indices_for_instances(
                    image_rgb.copy(), instances, start_instance_id)

                return predictions, vis_output, indices_outputs, indices_values

        return predictions, vis_output, None, None

    def calculate_vegetation_indices_for_instances(self, image, instances, start_instance_id=1):
        """计算每个实例的所有植被指数
        
        Args:
            image (np.ndarray): RGB格式的图像
            instances: 检测到的实例
            start_instance_id (int): 实例编号的起始值

        Returns:
            dict: 每个植被指数的可视化图像
            dict: 每个实例的所有植被指数值
        """
        # 创建颜色映射
        cmap = plt.cm.RdYlGn  # 使用Red-Yellow-Green颜色映射
        
        # 初始化返回值
        indices_outputs = {}  # 存储每个指数的可视化图像
        indices_values = {
            'ExG': [], 'VARI': [], 'GRVI': [], 'GLI': [], 'CVI': [], 'CIVE': [], 
            'TGI': [], 'VGI': [], 'ExR': [], 'ExGR': [], 'NDI': [], 'MGRVI': [], 
            'EGRBDI': [], 'RGBRI': [], 'E-NGBDI': [], 'ExB': [], 'IKA_W': []
        }
        
        # 为每个植被指数创建输出图像
        for index_name in indices_values.keys():
            indices_outputs[index_name] = image.copy()
        
        # 对每个实例计算植被指数
        for i in range(len(instances)):
            mask = instances.pred_masks[i].cpu().numpy()

            mask_image = image.copy()
            mask_image[~mask] = 0
            # 提取RGB值并归一化
            r = mask_image[:, :, 0].astype(np.float32) / 255.0
            g = mask_image[:, :, 1].astype(np.float32) / 255.0
            b = mask_image[:, :, 2].astype(np.float32) / 255.0
            
            # 只处理当前实例区域
            valid_pixels = mask
            
            # 计算所有植被指数
            indices = {
                'ExG': 2 * g - r - b,  # Excess Green Index
                'VARI': np.where((g + r - b) != 0, (g - r) / (g + r - b), 0),  # Visible Atmospherically Resistant Index
                'GRVI': np.where((g + r) != 0, (g - r) / (g + r), 0),  # Green Red Vegetation Index
                'GLI': np.where((g + r + b) != 0, (g - r - b) / (g + r + b), 0),  # Green Leaf Index
                'CVI': g - (g + b) / 2,  # Color Vegetation Index
                'CIVE': np.where((g + (0.5 * r + 0.5 * b)) != 0, (g - (0.5 * r + 0.5 * b)) / (g + (0.5 * r + 0.5 * b)), 0),  # Color Index of Vegetation
                'TGI': g - 0.69 * r - 0.31 * b,  # Triangular Greenness Index
                'VGI': np.where((g + b) != 0, g / (g + b), 0),  # Vegetation Index
                'ExR': 1.4 * r - g,  # Excess Red Index
                'ExGR': (2 * g - r - b) - (1.4 * r - g),  # Excess Green minus Excess Red
                'NDI': np.where((g + r) != 0, (g - r) / (g + r), 0),  # Normalized Difference Index
                'MGRVI': np.where((g ** 2 + r ** 2) != 0, (g ** 2 - r) / (g ** 2 + r ** 2), 0),  # Modified Green-Red Vegetation Index
                'EGRBDI': np.where(((2 * g) ** 2 + r * b) != 0, (2 * g - r * b) / ((2 * g) ** 2 + r * b), 0),  # Excess Green-Red-Blue Difference Index
                'RGBRI': np.where((g ** 2 + r * b) != 0, (g ** 2 - r * b) / (g ** 2 + r * b), 0),  # Red-Green-Blue Ratio Index
                'E-NGBDI': np.where((g ** 2 + b ** 2) != 0, (g ** 2 - b) / (g ** 2 + b ** 2), 0),  # Enhanced Normalized Green-Blue Difference Index
                'ExB': np.where((g + r + b) != 0, (1.4 * b - g) / (g + r + b), 0),  # Excess Blue Index
                'IKA_W': np.where((r + b) != 0, (r - b) / (r + b), 0),  # Kawashima Index
            }
            
            # 为每个指数计算平均值并生成可视化
            for index_name, index_values in indices.items():
                # 只考虑mask内的值
                masked_values = index_values * mask
                # 归一化当前实例的指数值

                if valid_pixels.any():
                    masked_values[valid_pixels] = self.normalize(masked_values[valid_pixels])

                # 计算平均值
                valid_values = masked_values[valid_pixels]
                if len(valid_values) > 0:
                    indices_values[index_name].append(np.mean(valid_values))
                else:
                    indices_values[index_name].append(0)


                # 创建热力图
                colored_index = np.zeros((image.shape[0], image.shape[1], 3), dtype=np.uint8)
                colored_pixels = cmap(masked_values[mask])[:, :3]
                colored_index[mask] = (colored_pixels * 255).astype(np.uint8)
                
                # 将热力图与原图混合
                alpha = 0.7
                mask_3d = np.stack([mask] * 3, axis=2)
                blended = cv2.addWeighted(indices_outputs[index_name], 1 - alpha, colored_index, alpha, 0)
                indices_outputs[index_name] = np.where(mask_3d, blended, indices_outputs[index_name])
                
                # 添加实例编号
                y_indices, x_indices = np.where(mask)
                if len(y_indices) > 0 and len(x_indices) > 0:
                    center_y = int(np.mean(y_indices))
                    center_x = int(np.mean(x_indices))
                    cv2.putText(indices_outputs[index_name], str(start_instance_id + i),
                               (center_x, center_y), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            
        # 为每个指数的输出图像添加颜色条
        for index_name in indices_outputs.keys():
            output_image = indices_outputs[index_name]
            colorbar_width = 50
            colorbar_height = output_image.shape[0]
            colorbar = np.zeros((colorbar_height, colorbar_width, 3), dtype=np.uint8)
            
            # 生成颜色条
            for y in range(colorbar_height):
                value = 1 - (y / colorbar_height)
                color = cmap(value)[:3]
                colorbar[y, :] = (np.array(color) * 255).astype(np.uint8)
            
            # 添加刻度
            num_ticks = 11
            for i in range(num_ticks):
                y_pos = int(i * (colorbar_height - 1) / (num_ticks - 1))
                cv2.line(colorbar, (0, y_pos), (10, y_pos), (255, 255, 255), 1)
                value = 1 - (i / (num_ticks - 1))
                cv2.putText(colorbar, f'{value:.1f}', (15, y_pos + 5),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
            
            # 将颜色条添加到图像右侧
            final_image = np.zeros((output_image.shape[0], output_image.shape[1] + colorbar_width, 3), dtype=np.uint8)
            final_image[:, :output_image.shape[1]] = output_image
            final_image[:, output_image.shape[1]:] = colorbar
            
            # 添加指数名称
            cv2.putText(final_image, index_name, (output_image.shape[1] + 5, 20),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            indices_outputs[index_name] = final_image

        return indices_outputs, indices_values

    def normalize(self, array):
        if array.size == 0:
            raise ValueError("Input array is empty. Cannot perform normalization on an empty array.")

        min_val = np.min(array)
        max_val = np.max(array)

        if max_val > min_val:
            normalized_array = (array - min_val) / (max_val - min_val+1e-8)
        else:
            normalized_array = 0.5
        return normalized_array

    def calculate_vari_for_instances(self, image, instances, start_instance_id=1):
        """计算每个实例内部的像素级植被指数 (VGI - Vegetation Greenness Index)
        VGI = (G - R) / (G + R + B)
        
        Args:
            image (np.ndarray): RGB格式的图像
            instances: 检测到的实例
            start_instance_id (int): 实例编号的起始值

        Returns:
            np.ndarray: 可视化的植被指数图
            list: 每个实例的平均植被指数值
        """
        # 创建颜色映射
        cmap = plt.cm.RdYlGn  # 使用Red-Yellow-Green颜色映射，VGI值越大表示植被越健康
        
        # 创建输出图像
        output_image = image.copy()
        vgi_values = []  # 存储每个实例的平均VGI值
        
        # 创建一个掩码来标记所有已处理的像素
        processed_mask = np.zeros((image.shape[0], image.shape[1]), dtype=bool)
        
        # 对每个实例单独计算和显示VGI
        for i in range(len(instances)):
            mask = instances.pred_masks[i].cpu().numpy()
            
            # 只处理当前实例区域的RGB值
            masked_image = image.copy()
            masked_image[~mask] = 0  # 将非实例区域置0
            
            # 提取RGB值并归一化
            r = masked_image[:, :, 0].astype(np.float32) / 255.0
            g = masked_image[:, :, 1].astype(np.float32) / 255.0
            b = masked_image[:, :, 2].astype(np.float32) / 255.0
            
            # 计算VGI: (G - R) / (G + R + B)
            vgi = np.zeros_like(r)
            valid_pixels = mask
            denominator = g[valid_pixels] + r[valid_pixels] + b[valid_pixels] + 1e-8  # 添加小值以避免除零
            vgi[valid_pixels] = 2*g[valid_pixels]-r[valid_pixels]-b[valid_pixels]
            
            # 对当前实例的VGI值进行最小-最大归一化
            if valid_pixels.any():
                vgi_min = np.min(vgi[valid_pixels])
                vgi_max = np.max(vgi[valid_pixels])
                if vgi_max > vgi_min:
                    vgi[valid_pixels] = (vgi[valid_pixels] - vgi_min) / (vgi_max - vgi_min)
                else:
                    vgi[valid_pixels] = 0.5  # 如果最大值等于最小值，设为中间值
            
            # 计算当前实例的平均VGI值（用于统计）
            valid_vgi = vgi[valid_pixels]
            if len(valid_vgi) > 0:
                vgi_values.append(np.mean(valid_vgi))
            else:
                vgi_values.append(0)
            
            # 创建热力图
            colored_vgi = np.zeros((image.shape[0], image.shape[1], 3), dtype=np.uint8)
            
            # 只对实例内部的像素应用颜色映射
            colored_pixels = cmap(vgi[mask])[:, :3]  # 去掉alpha通道
            colored_vgi[mask] = (colored_pixels * 255).astype(np.uint8)
            
            # 将热力图与原图混合
            alpha = 0.7  # 调整透明度
            mask_3d = np.stack([mask] * 3, axis=2)
            blended = cv2.addWeighted(output_image, 1 - alpha, colored_vgi, alpha, 0)
            output_image = np.where(mask_3d, blended, output_image)
            
            # 添加实例编号
            y_indices, x_indices = np.where(mask)
            if len(y_indices) > 0 and len(x_indices) > 0:
                center_y = int(np.mean(y_indices))
                center_x = int(np.mean(x_indices))
                cv2.putText(output_image, str(start_instance_id + i), (center_x, center_y),
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

        # 添加颜色条
        colorbar_width = 50
        colorbar_height = output_image.shape[0]
        colorbar = np.zeros((colorbar_height, colorbar_width, 3), dtype=np.uint8)

        # 生成颜色条
        for y in range(colorbar_height):
            value = 1 - (y / colorbar_height)  # 从上到下，1到0
            color = cmap(value)[:3]  # 使用相同的颜色映射
            colorbar[y, :] = (np.array(color) * 255).astype(np.uint8)

        # 在颜色条上添加刻度
        num_ticks = 11  # 0到1，每0.1一个刻度
        for i in range(num_ticks):
            y_pos = int(i * (colorbar_height - 1) / (num_ticks - 1))
            # 添加刻度线
            cv2.line(colorbar, (0, y_pos), (10, y_pos), (255, 255, 255), 1)
            # 添加刻度值（从1到0）
            value = 1 - (i / (num_ticks - 1))  # 从1到0
            cv2.putText(colorbar, f'{value:.1f}', (15, y_pos + 5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)

        # 将颜色条添加到原图右侧
        final_image = np.zeros((output_image.shape[0], output_image.shape[1] + colorbar_width, 3), dtype=np.uint8)
        final_image[:, :output_image.shape[1]] = output_image
        final_image[:, output_image.shape[1]:] = colorbar

        # 添加颜色条标题
        cv2.putText(final_image, 'VGI', (output_image.shape[1] + 5, 20),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

        return final_image, vgi_values

    def calculate_vari(self, image, mask):
        """计算植被指数 VARI (Visible Atmospherically Resistant Index)
        Args:
            image (np.ndarray): RGB格式的图像
            mask (np.ndarray): 二值掩码

        Returns:
            np.ndarray: 归一化的植被指数图
        """
        # 提取RGB波段并归一化到[0,1]范围
        r = image[:, :, 0].astype(np.float32) / 255.0
        g = image[:, :, 1].astype(np.float32) / 255.0
        b = image[:, :, 2].astype(np.float32) / 255.0

        # 计算VARI: (Green - Red) / (Green + Red - Blue)
        # 添加小值以避免除零
        epsilon = 1e-8
        vari = (g - r) / (g + r - b + epsilon)

        if np.any(mask):
            exg_masked = vari[mask]
            exg_min = exg_masked.min()
            exg_max = exg_masked.max()

            if exg_max > exg_min:
                # 只归一化mask区域内的值
                exg_norm = np.zeros_like(vari)
                exg_norm[mask] = (vari[mask] - exg_min) / (exg_max - exg_min)
                return exg_norm

        return vari

    def _frame_from_video(self, video):
        while video.isOpened():
            success, frame = video.read()
            if success:
                yield frame
            else:
                break

    def run_on_video(self, video):
        """
        Visualizes predictions on frames of the input video.

        Args:
            video (cv2.VideoCapture): a :class:`VideoCapture` object, whose source can be
                either a webcam or a video file.

        Yields:
            ndarray: BGR visualizations of each video frame.
        """
        video_visualizer = VideoVisualizer(self.metadata, self.instance_mode)

        def process_predictions(frame, predictions):
            frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            if "panoptic_seg" in predictions:
                panoptic_seg, segments_info = predictions["panoptic_seg"]
                vis_frame = video_visualizer.draw_panoptic_seg_predictions(
                    frame, panoptic_seg.to(self.cpu_device), segments_info
                )
            elif "instances" in predictions:
                predictions = predictions["instances"].to(self.cpu_device)
                vis_frame = video_visualizer.draw_instance_predictions(frame, predictions)
            elif "sem_seg" in predictions:
                vis_frame = video_visualizer.draw_sem_seg(
                    frame, predictions["sem_seg"].argmax(dim=0).to(self.cpu_device)
                )

            # Converts Matplotlib RGB format to OpenCV BGR format
            vis_frame = cv2.cvtColor(vis_frame.get_image(), cv2.COLOR_RGB2BGR)
            return vis_frame

        frame_gen = self._frame_from_video(video)
        if self.parallel:
            buffer_size = self.predictor.default_buffer_size

            frame_data = deque()

            for cnt, frame in enumerate(frame_gen):
                frame_data.append(frame)
                self.predictor.put(frame)

                if cnt >= buffer_size:
                    frame = frame_data.popleft()
                    predictions = self.predictor.get()
                    yield process_predictions(frame, predictions)

            while len(frame_data):
                frame = frame_data.popleft()
                predictions = self.predictor.get()
                yield process_predictions(frame, predictions)
        else:
            for frame in frame_gen:
                yield process_predictions(frame, self.predictor(frame))

    def calculate_vari_for_video_frame(self, frame, predictions):
        frame_rgb = frame[:, :, ::-1]
        instances = predictions["instances"].to(self.cpu_device)
        frame_rgb, _ = self.calculate_vari_for_instances(frame_rgb, instances)
        return frame_rgb


class AsyncPredictor:
    """
    A predictor that runs the model asynchronously, possibly on >1 GPUs.
    Because rendering the visualization takes considerably amount of time,
    this helps improve throughput a little bit when rendering videos.
    """

    class _StopToken:
        pass

    class _PredictWorker(mp.Process):
        def __init__(self, cfg, task_queue, result_queue):
            self.cfg = cfg
            self.task_queue = task_queue
            self.result_queue = result_queue
            super().__init__()

        def run(self):
            predictor = DefaultPredictor(self.cfg)

            while True:
                task = self.task_queue.get()
                if isinstance(task, AsyncPredictor._StopToken):
                    break
                idx, data = task
                result = predictor(data)
                self.result_queue.put((idx, result))

    def __init__(self, cfg, num_gpus: int = 1):
        """
        Args:
            cfg (CfgNode):
            num_gpus (int): if 0, will run on CPU
        """
        num_workers = max(num_gpus, 1)
        self.task_queue = mp.Queue(maxsize=num_workers * 3)
        self.result_queue = mp.Queue(maxsize=num_workers * 3)
        self.procs = []
        for gpuid in range(max(num_gpus, 1)):
            cfg = cfg.clone()
            cfg.defrost()
            cfg.MODEL.DEVICE = "cuda:{}".format(gpuid) if num_gpus > 0 else "cpu"
            self.procs.append(
                AsyncPredictor._PredictWorker(cfg, self.task_queue, self.result_queue)
            )

        self.put_idx = 0
        self.get_idx = 0
        self.result_rank = []
        self.result_data = []

        for p in self.procs:
            p.start()
        atexit.register(self.shutdown)

    def put(self, image):
        self.put_idx += 1
        self.task_queue.put((self.put_idx, image))

    def get(self):
        self.get_idx += 1  # the index needed for this request
        if len(self.result_rank) and self.result_rank[0] == self.get_idx:
            res = self.result_data[0]
            del self.result_data[0], self.result_rank[0]
            return res

        while True:
            # make sure the results are returned in the correct order
            idx, res = self.result_queue.get()
            if idx == self.get_idx:
                return res
            insert = bisect.bisect(self.result_rank, idx)
            self.result_rank.insert(insert, idx)
            self.result_data.insert(insert, res)

    def __len__(self):
        return self.put_idx - self.get_idx

    def __call__(self, image):
        self.put(image)
        return self.get()

    def shutdown(self):
        for _ in self.procs:
            self.task_queue.put(AsyncPredictor._StopToken())

    @property
    def default_buffer_size(self):
        return len(self.procs) * 5
