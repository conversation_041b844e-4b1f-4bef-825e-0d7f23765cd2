_BASE_: Base-PointRend-Semantic-FPN.yaml
MODEL:
  WEIGHTS: detectron2://ImageNetPretrained/MSRA/R-101.pkl
  RESNETS:
    DEPTH: 101
  SEM_SEG_HEAD:
    NUM_CLASSES: 19
  POINT_HEAD:
    NUM_CLASSES: 19
    TRAIN_NUM_POINTS: 2048
    SUBDIVISION_NUM_POINTS: 8192
DATASETS:
  TRAIN: ("cityscapes_fine_sem_seg_train",)
  TEST: ("cityscapes_fine_sem_seg_val",)
SOLVER:
  BASE_LR: 0.01
  STEPS: (40000, 55000)
  MAX_ITER: 65000
  IMS_PER_BATCH: 32
INPUT:
  MIN_SIZE_TRAIN: (512, 768, 1024, 1280, 1536, 1792, 2048)
  MIN_SIZE_TRAIN_SAMPLING: "choice"
  MIN_SIZE_TEST: 1024
  MAX_SIZE_TRAIN: 4096
  MAX_SIZE_TEST: 2048
  CROP:
    ENABLED: True
    TYPE: "absolute"
    SIZE: (512, 1024)
    SINGLE_CATEGORY_MAX_AREA: 0.75
  COLOR_AUG_SSD: True
DATALOADER:
  NUM_WORKERS: 10
